import { Art, Canvas, Point, Path, Polygon, Rectangle, Ellipse, Triangle, House, SwedishHouse, SwedishChurch, SwedishDom, HouseWindow, Scene, Drawable, Style, Landscape, LandscapeGrass, LandscapeGround, LandscapeSnow, LandscapeFlowers, LandscapeFence, LandscapeBush, Tools, Noise, Config, Tracking, Tree, Sun, SunLayer, generateGreenlandicName } from './src/art/index.js';

import Stats from 'stats.js';



const sp = new URLSearchParams(window.location.search)
//  console.log(sp);

// this is how to define parameters
$fx.params([
    {
        id: "house_density",
        name: "House density",
        type: "number",
        options: {
          min: 0.1,
          max: 1,
          step: 0.05,
        },
      },
      {
        id: "house_prob",
        name: "House probability",
        type: "number",
        options: {
          min: -0.5,
          max: 1,
          step: 0.05,
        },
      },
      {
        id: "mountain_slope",
        name: "Mountain slope",
        type: "select",
        //default: "pear",
        options: {
          options: ["64", "92", "128","1024"],
        },
      },
      {
        id: "contour_density",
        name: "Contour line density",
        type: "number",
        options: {
          min: 0.5,
          max: 2.0,
          step: 0.1,
        },
      },
      {
        id: "contour_noise_scale",
        name: "Contour noise scale",
        type: "number",
        options: {
          min: 0.0001,
          max: 0.01,
          step: 0.0001,
        },
      },
      {
        id: "sky_contour_opacity",
        name: "Sky contour opacity",
        type: "number",
        options: {
          min: 0.05,
          max: 0.5,
          step: 0.05,
        },
      },
      {
        id: "day_time",
        name: "Day time",
        type: "select",
        options: {
          options: ["morning", "day", "evening", "night"],
        },
      },
      {
        id: "fog",
        name: "Fog",
        type: "boolean",
      
      },

  {
    id: "prob_layer_occupation",
    name: "Prob. layer occupation",
    type: "number",
    //default: Math.PI,
    options: {
      min: 0.2,
      max: 1,
      step: 0.1,
    },
  },

  {
    id: "bigint_id",
    name: "A bigint",
    type: "bigint",
    update: "code-driven",
    //default: BigInt(Number.MAX_SAFE_INTEGER * 2),
    options: {
      min: Number.MIN_SAFE_INTEGER * 4,
      max: Number.MAX_SAFE_INTEGER * 4,
      step: 1,
    },
  },
  {
    id: "string_id_long",
    name: "A string long",
    type: "string",
    update: "code-driven",
    //default: "hello",
    options: {
      minLength: 1,
      maxLength: 512,
    },
  },
  {
    id: "select_id",
    name: "A selection",
    type: "select",
    update: "code-driven",
    //default: "pear",
    options: {
      options: ["apple", "orange", "pear"],
    },
  },
  {
    id: "color_id",
    name: "A color",
    type: "color",
    update: "code-driven",
    //default: "ff0000",
  },
  {
    id: "boolean_id",
    name: "A boolean",
    type: "boolean",
    update: "code-driven",
    //default: true,
  },
  {
    id: "string_id",
    name: "A string",
    type: "string",
    update: "code-driven",
    //default: "hello",
    options: {
      minLength: 1,
      maxLength: 512,
    },
  },
])

// this is how features can be defined
//todo posar aqui
$fx.features({
  "A random feature": Math.floor($fx.rand() * 10),
  "A random boolean": $fx.rand() > 0.5,
  "A random string": ["A", "B", "C", "D"].at(Math.floor($fx.rand() * 4)),
  "Day time": $fx.getParam("day_time"),
})


$fx.on(
    "params:update",
    newRawValues => {
      // opt-out default behaviour
      if (newRawValues.number_id === 5) return false
      // opt-in default behaviour
      return true
    },
    (optInDefault, newValues) => main()
  )




        const canvas = document.getElementById('canvas');

        // Base grid_dist value - used as a reference, but individual layers will have scaled values
        Art.grid_dist=50*1

        Art.width=1920
        Art.height=1080



      //  const ANIMATION_SPEED=100


        let mycanvas=new Canvas(canvas)

       // mycanvas.setRatio(1)

        let tracking
        //todo fer millor

        let stats
        let city_name

        const config=new Config()
        config.load('./data/config.json').then(() => {
            console.log("tinc config",config)
            if(config.get("debug")){

                 stats = Stats()
                document.body.appendChild(stats.dom)
            }

            mycanvas.setRatio(config.get("ratio"))
            tracking=new Tracking(canvas,config.get("mode"))

            if(config.get("auto_refresh",0)!=0){
                setTimeout(() => {
                    window.location.reload();
                }, config.get("auto_refresh") * 1000);
            }
            city_name = generateGreenlandicName()


            // Update the city name in the HTML overlay
            document.getElementById('city-name').textContent = city_name;

            initApp();
        });


        function initApp(){

            const noise=new Noise()

            const scene = new Scene(canvas);

            // Get base sky colors for gradient (store as global variables)
            window.skyTopColor = Art.palette.getRandColor("sky");
            // Create a middle color by slightly lightening the top color
            window.skyMiddleColor = Art.palette.getColorLighterEx(window.skyTopColor, 0.15);
            // Create a bottom color by further lightening the top color
            window.skyBottomColor = Art.palette.getColorLighterEx(window.skyTopColor, 0.3);

            // Create a dedicated sun layer (the farthest one)
            // We'll use a negative z-index to ensure it's always behind all other layers
            const sunLayer = new SunLayer(
                mycanvas.width,
                mycanvas.height
            );

            // Add the sun layer to the scene with z-index -1 (farthest)
            scene.add(sunLayer, -1);


        let land=new Landscape(mycanvas)
        let churchs=0
        let doms=0



        // Create multiple layers for better perspective effect
        land.createLayers()
            console.log("land",land)

        for(let i=0;i<land.layers.length-1;i++){
            churchs=0
            // Get layer-specific grid distance for properly scaled objects
            const layerGridDist = land.layers[i].grid_dist || Art.grid_dist;


            // Track if any objects are added to this layer
            let elementsAddedToLayer = false;

            //console.log(land.layers[i])


            land.layers[i].setStyle(
                new Style({fillStyle:Art.palette.getRandColor("landscape")
            }));

            // Use consistent z-indexing that matches the visual depth
            // Back layers (index 0) have smaller grid_dist (25) and lower z-index
            // Front layers (higher indices) have larger grid_dist (100) for perspective
            scene.addLayer(land.layers[i],i);
            if(Art.rand()<$fx.getParam("prob_layer_occupation") ){  //i=0 es l'ultim
            land.registerDrawables(i,function(p){

                let n=noise.noise2D(p.x,p.y,0.0001*layerGridDist);
                if(!p.visible){
                    //no existeixen)
                }else{
                    // Set temporary grid_dist to the layer value for proper scaling of objects
                    const originalGridDist = Art.grid_dist;
                    Art.grid_dist = layerGridDist;

                    function getObjectWidth(num){
                        if(num >= 2){
                            if(num>6) num=6
                            return Tools.randPattern({2:0.5,3:0.5,4:0.2,5:0.2,6:0.05},num)
                        }else{
                            return 0;
                        }
                    }
                    function markOccupied(p,num){
                        for(let i=0; i<num; i++){
                            p.nextPoints[i].type = "ncasa";
                        }
                    }

                    if(Art.rand()<$fx.getParam("house_density") && n<$fx.getParam("house_prob") && p.type==undefined){  //n param
                        const num=getObjectWidth(p.nextPoints.length)

                        markOccupied(p,num)

                        var house
                        if(Art.rand()<0.8){ // Increased probability of churches (20% instead of 1%)
                         // Pass the layer index (i) to adjust saturation based on distance
                         // Reverse the index so that 0 is furthest and higher numbers are closer
                         // This makes the furthest layers (higher i) more desaturated
                         const layerIndex = land.layers.length - 1 - i;
                         house = SwedishHouse.getHouse(num, p, layerIndex);

                        scene.add(house, i);
                        }else{


                            if(num>4 && doms<1 && Art.rand()<1){
                                house = new SwedishDom(
                                    p,
                                );
                                house.layerIndex = land.layers.length - 1 - i;
                                doms++

                                scene.add(house, i);
                            }else if(num>2 && churchs<4){
                                house = new SwedishChurch(
                                    p,
                                    num
                                );
                                // Also store the layer index for churches
                                house.layerIndex = land.layers.length - 1 - i;
                                churchs++

                             scene.add(house, i);
                            }
                        }

                    }

                    if(Art.rand()<0.2 ){
                        snow=new LandscapeSnow(
                            p,
                            1
                        );
                        scene.add(snow,i);
                    }


                if(Art.rand()<0.5){
                    if(p.nextPoints.length>1){
                        /*let snow=new LandscapeSnow(
                            p,
                            1
                        );*/
                        var snow
                        if(Art.rand()<0.5){
                             snow=new LandscapeSnow(
                                p,
                                1
                            );
                        }else{
                                snow=new Tree(
                                p,
                                1
                            );
                    }

                       // scene.add(snow,i);

                    }
                }




                // Restore the original grid_dist after creating objects for this point
                Art.grid_dist = originalGridDist;
            }

            });


        }

        }

        // Create all layer bitmaps in advance for better performance
        scene.createLayerBitmaps();
        console.log("scene",scene)
        let xx = 0; // Animation time variable
        let targetX = 0; // Target position for parallax effect
        let targetY = 0;



        setInterval(() => {
            // Recreate the layer bitmaps to update the clock
            updateBitmaps();
        }, 40*5*1); // Update every minute (60000 ms)

        function updateBitmaps() {
            // Get the city name overlay element
            const cityNameOverlay = document.getElementById('city-name-overlay');

            if (!scene.allPainted()) {
                // If not all painted, recreate the layer bitmaps
                scene.createLayerBitmaps();

                // Make sure the city name is in the center position
                if (cityNameOverlay && cityNameOverlay.classList.contains('corner-position')) {
                    cityNameOverlay.classList.remove('corner-position');
                }
            } else {
                // All painted - move the city name to the bottom right corner
                if (cityNameOverlay && !cityNameOverlay.classList.contains('corner-position')) {
                    cityNameOverlay.classList.add('corner-position');

                    // Ensure the city name is fully visible in the bottom right corner
                    const cityNameElement = document.getElementById('city-name');
                    if (cityNameElement) {
                        // Make sure the text is visible by ensuring it's not too long
                        if (city_name.length > 15) {
                            // If the name is too long, truncate it to ensure visibility
                            cityNameElement.textContent = city_name.substring(0, 15);
                        }
                    }

                    console.log('City name moved to corner position');
                }
            }
        }



        animate()


       // land.debug()

        function animate(){
            // Draw the sky gradient directly on the canvas
            // Create a linear gradient for the sky
            const skyGradient = Art.ctx.createLinearGradient(0, 0, 0, Art.height);
            skyGradient.addColorStop(0, window.skyTopColor); // Top color
            skyGradient.addColorStop(0.5, window.skyMiddleColor); // Middle color
            skyGradient.addColorStop(1, window.skyBottomColor); // Bottom color

            // Apply the gradient as fill style
            Art.ctx.fillStyle = skyGradient;

            // Fill the entire canvas with the gradient
            Art.fillRect(0, 0, Art.width, Art.height);

            //valors entre -1 i 1
            let tracking_values = tracking.getTracking()
            targetX = tracking_values[0]
            targetY = tracking_values[1]

            // Get parallax configuration values (or use defaults if not available)
            const parallaxConfig = config.get("parallax") || {};
            const parallaxStrength = parallaxConfig.strength || 100; // Default: 100
            const exaggeration = parallaxConfig.exaggeration || 1.0; // Default: 1.0 (no exaggeration)
            const waveAmplitude = parallaxConfig.waveAmplitude || 5; // Default: 5
            const waveFrequency = parallaxConfig.waveFrequency || 8; // Default: 8

            // Draw the sun layer (z-index -1) with minimal parallax effect
            // The sun should move very slightly to create a distant effect
            const sunParallaxFactor = 0.08; // Very small factor for distant sun

            // Calculate the maximum allowed movement based on the sun layer margin
            const sunLayer = scene.layers.find(layer => layer.zIndex === -1);
            const sunMargin = sunLayer ? sunLayer.margin || 0.3 : 0.3;

            // Calculate maximum allowed movement (80% of the margin to be safe)
            const maxSunMoveX = Art.width * sunMargin * 0.8;
            const maxSunMoveY = Art.height * sunMargin * 0.8;

            // Calculate initial position with parallax effect
            let sunPosX = -targetX * parallaxStrength * sunParallaxFactor;
            let sunPosY = -targetY * parallaxStrength * sunParallaxFactor;

            // Add a very subtle wave movement to the sun
            const sunWaveX = Math.sin(xx * waveFrequency * 0.5) * waveAmplitude * 0.1;
            const sunWaveY = Math.cos(xx * waveFrequency * 0.3) * waveAmplitude * 0.05;

            // Combine effects for sun position
            sunPosX += sunWaveX;
            sunPosY += sunWaveY;

            // Clamp the movement to stay within the safe bounds
            sunPosX = Math.max(Math.min(sunPosX, maxSunMoveX), -maxSunMoveX);
            sunPosY = Math.max(Math.min(sunPosY, maxSunMoveY), -maxSunMoveY);

            // Draw the sun layer
            scene.paintLayerBitmap(-1, sunPosX, sunPosY);

            // Draw each landscape layer with parallax effect
            for(let i=0; i<land.layers.length; i++){
                // We're reusing the parallax configuration values from above

                // Calculate layer depth factor - more exaggerated difference between layers
                // Use exponential function to create more dramatic difference between layers
                // Nearest layers (higher index) will move much more than distant layers
                const layerCount = land.layers.length;
                const normalizedIndex = i / (layerCount - 1); // 0 to 1
                const layerDepthFactor = Math.pow(normalizedIndex * exaggeration + 0.5, 2) * layerCount;

                // Calculate positions with increased strength:
                // - Inverse targetX because we want layers to move opposite to mouse direction
                // - Apply exponential depth factor for more dramatic effect

                let parallaxX = -targetX * parallaxStrength * (layerDepthFactor / layerCount);
                let parallaxY = -targetY * parallaxStrength * (layerDepthFactor / layerCount);


                // Add automatic wave movement with configurable amplitude and frequency
                // Distant layers (low index) have subtle movement, close layers have more pronounced movement
                const layerWaveFactor = 1 + (i / layerCount) * 2; // 1.0 to 3.0
                const waveX = Math.sin(xx * waveFrequency) * waveAmplitude * layerWaveFactor / (layerCount - i || 1);
                const waveY = Math.cos(xx * (waveFrequency * 0.6)) * (waveAmplitude * 0.6) * layerWaveFactor / (layerCount - i || 1);

                // Combine parallax and wave effects
                let posX = parallaxX + waveX;
                let posY = parallaxY + waveY;

                // Get layer dimensions to keep within canvas bounds
                const layer = land.layers[i];
                const margin = layer && layer.margin ? layer.margin : 0;

                // Calculate max allowed movement for parallax
                // The margin defines how much extra space we have around the content
                // This space can be used for movement while keeping content visible
                const maxOffsetX = margin * Art.width * Art.ratio / 2;
                const maxOffsetY = margin * Art.height * Art.ratio / 2;

                // Clamp positions to keep content visible within canvas bounds
                posX = Math.max(Math.min(posX, maxOffsetX), -maxOffsetX);
                posY = Math.max(Math.min(posY, maxOffsetY), -maxOffsetY);

                // Layer bitmap drawing handles the margin offset internally
                scene.paintLayerBitmap(i, posX, posY);
            }

            // Update animation variables
            xx += 0.002;
            if(config.get("debug")){
                tracking.debug();
            }
            // Continue animation loop
            requestAnimationFrame(animate);

            if(config.get("debug")) stats.update()
        }

    }
