# Hockney2 Project Guidelines

## Build Commands
- `npm run start` - Start the development server with live reloading (uses Parcel)
- `npm run build` - Build the project for production

## Development Environment
- Project uses Parcel bundler to handle building and serving
- No testing framework is currently set up

## Code Style Guidelines

### Naming & Organization
- Classes: PascalCase (e.g., `Point`, `Scene`)
- Methods/properties: camelCase (e.g., `setStyle`, `movePolar`)
- Constants/static properties: camelCase or UPPERCASE (e.g., `grid_dist`, `UP`)
- Each class should be in its own file with matching filename

### Imports & Exports
- Use ES Modules syntax (`import`/`export`) 
- Include `.js` extension in imports
- Use the central `index.js` file for library components
- Prefer named exports over default exports

### Class Structure
- Object-oriented approach with inheritance
- Return `this` in setter methods to allow method chaining
- Static utility methods grouped in appropriate utility classes
- Follow single responsibility principle for methods and classes

### Formatting
- 2-space indentation
- Opening braces on the same line
- Spaces around operators
- Single space after control statements

### Error Handling
- Use defensive programming (null checks, etc.) 
- Preserve existing patterns for handling edge cases