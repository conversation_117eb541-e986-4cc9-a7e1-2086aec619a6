{"name": "hockney2", "version": "1.0.0", "description": "", "source": "index.html", "scripts": {"clean-dist": "rm -rf dist && mkdir dist", "copy-data": "mkdir -p dist/data && cp data/config.json dist/data/", "start": "parcel src/index.html", "build": "parcel build src/index.html", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"perlin-simplex": "^0.0.3", "simplex-noise": "^4.0.3", "stats.js": "^0.17.0"}}