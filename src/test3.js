import { Art, Canvas, Point, Path, Polygon, Rectangle, Ellipse, Triangle, House, SwedishHouse, SwedishChurch, SwedishDom, HouseWindow, Scene, Drawable, Style, Landscape, LandscapeGrass, LandscapeGround, LandscapeSnow, LandscapeFlowers, LandscapeFence, LandscapeBush, Tools, Palette, Noise } from './art/index.js';

const canvas = document.getElementById('canvas');
    
// Base grid_dist value - used as a reference, but individual layers will have scaled values
Art.grid_dist=50*1

Art.width=1920 
Art.height=1080

let mycanvas=new Canvas(canvas)
mycanvas.setRatio(1)



// Create a scene with multiple layers
const scene = new Scene();


const layerGridDist = 50;
Art.grid_dist = layerGridDist;


let land=new Landscape(mycanvas)



const p=new Point(  200, 400) 
const house1 = SwedishHouse.getHouse(6,p)


/*
const house2 = new SwedishHouse(
    new Point(  200+300, 400),
    2+Tools.randInt(4), 
    2+Tools.randInt(2), 
);

house2.flipHorizontal();


const house3 = new SwedishHouse(
    new Point(  200+300*2, 400),
    4 ,
    2+Tools.randInt(4), 
    2+Tools.randInt(2), 
);
*/

Art.ctx.fillRect(0,0,Art.width,Art.height)

scene.add(house1, 0);

let w=Tools.randInt(4)+3

const house2=new SwedishChurch(
    new Point(  200+500, 600),w
);
scene.add(house2, 0);


const dom=new SwedishDom(
    new Point(  200+500+300, 900),w
);

scene.add(dom, 0);




scene.paint()
