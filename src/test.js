import { Art } from './art/Art.js';
import { Scene } from './art/Scene.js';
import { Point } from './art/Point.js';
import { SwedishHouse, SwedishChurch } from './art/SwedishHouse.js';
import { Palette } from './art/Palette.js';
import { Canvas } from './art/Canvas.js';

// Initialize the scene
const canvas = document.getElementById('canvas');
if (!canvas) {
  console.error('Canvas not found! Make sure you have a canvas with id="canvas" in your HTML.');
} else {
  // Set up the art context
  Art.ctx = canvas.getContext('2d');
  Art.width=1920 
  Art.height=1080
  Art.grid_dist = 50;
  

  
  
let mycanvas=new Canvas(canvas)
 mycanvas.setRatio(1)

          
  // Create a palette for the scene
  if (!Art.palette) {
    Art.palette = new Palette();
  }
  
  // Create a scene with multiple layers
  const scene = new Scene();
  
  // Create test objects
  const houses = [];
  const churches = [];
  

  Art.grid_dist = 25
  // Create houses for layer 0
  for (let i = 0; i < 32; i++) {
    const house = new SwedishHouse(
      new Point( i * 100, 400),
      4 ,
      3 
    );
    houses.push(house);
    scene.add(house, 0);
  }
  

  Art.grid_dist = 50
  // Create churches for layer 1
  for (let i = 0; i < 2; i++) {
    const church = new SwedishChurch(
      new Point(200 + i * 300, 500),
      3 ,
      2 
    );
    churches.push(church);
    scene.add(church, 1);
  }

  scene.createLayerBitmaps();
  //scene.paintAllLayerBitmaps();
  scene.paint()
}