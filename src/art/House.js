
import { Point } from './Point.js';
import { Art } from './Art.js';
import { Polygon } from './Polygon.js';
import { MultiPolygon } from './MultiPolygon.js';


export class House extends MultiPolygon {

    sunIntensity = 0.1; //param 0.05 -0.15
    static roof_angle=1.5 //param 1.5 2 2.5 3 3.5
    constructor(point, gridd_w, gridd_h, type="house") {
        super();

        this.type = type;
        // Store the current grid_dist value for later use in paint method
        this.currentGridDist = Art.grid_dist;
        this.width = gridd_w*Art.grid_dist;
        this.height = gridd_h*Art.grid_dist;

        /* no fa res
        this.setStyle({
            lineWidth: 14
        });*/

        // Save the bottom left point
        this.point = point;


        //param fer dos tipus de casa, amb el costat mes ample o no
        let factorh = 0.; //param


        if(gridd_w==5 && gridd_h==3){
            factorh=3/(5*House.roof_angle)
        }
        factorh=gridd_h/(gridd_w*House.roof_angle)
        /*
        if(gridd_w==3 && gridd_h==2){
            factorh=0.25
        }
        if(gridd_w==6 && gridd_h==4){
            factorh=0.25
        }*/
        let factorv = 0.5; //param

        // Bottom points
        let b0 = point; // bottom left is now our starting point


        let b2 = point.move(this.width, 0); // bottom right

        // Instead of moving upward with negative height, we need positive values
        // as the canvas y-coordinate increases downward
        let t0 = b0.move(0, -this.height); // top left (moving up means decreasing y)
        let t2 = b2.move(0, -this.height); // top right

        // Middle points
        let t1 = t0.getMiddle(t2, factorh); // middle top
        let tp = t0.getMiddle(t1, 0.5); // peak point

        // Middle height points
        let m0 = b0.getMiddle(t0, factorv); // middle left
        let m2 = b2.getMiddle(t2, factorv); // middle right
        let m1 = m0.getMiddle(m2, factorh); // middle center
        let b1 = b0.move(this.width * factorh, 0); // middle bottom

        let d = t0.distanceTo(tp);
        let tr = t2.move(-d, 0); // top right roof point

        // Define the walls using the new coordinates
        // Create walls
        this.frontalWall = new Polygon([
            m0, tp, m1, b1, b0
        ]);


        this.lateralWall = new Polygon([
            m1, m2, b2, b1
        ]);




       // const col=Art.palette.getRealRandomColor(0.3)
       // this.lateralWall.style.fillStyle=col
        //this.frontalWall.style.fillStyle =col

        // Cache the roof points for later use
        this.roofPoints = [m1, tp, tr, m2];

        // Randomly decide whether to add a door or windows to the frontal wall (50% chance)
        this.hasDoor = Art.rand() < 0.5;

        // Create the roof during initial construction, but don't style it yet
        const roof = new Polygon(this.roofPoints);
        this.addPolygon(roof, 2); // Roof with highest z-index
        this.addPolygon(this.frontalWall, 1); // Frontal wall with middle z-index
        this.addPolygon(this.lateralWall, 0); // Lateral wall with lowest z-index

        if (this.hasDoor) {
            // Add a door to the frontal wall
            let door = this.createDoor();
    
            this.addPolygon(door, 1); // Door on the frontal wall
        }

        // Create windows for both walls
        let windows = this.createWindows();
        windows.forEach(w => this.addPolygon(w, 1));

        this.createMoreStuff()
    }

    createMoreStuff(){
        //todo
    }

    createWindows() {
        // Create windows for the lateral wall
        const lateralWindows = this.createWindowsForWall(this.lateralWall);

        // If the house doesn't have a door, create windows for the frontal wall
        if (!this.hasDoor) {
            // Create frontal windows with the special pattern (1 on top, 2 on bottom)
            const frontalWindows = this.createFrontalWindows();
            return [...lateralWindows, ...frontalWindows];
        }

        // If the house has a door, only return lateral windows
        return lateralWindows;
    }

    /**
     * Creates windows for the frontal wall
     * - For small houses: 1 window in the center
     * - For larger houses: 3 windows (1 on top, 2 on bottom)
     * @returns {Array} Array of window polygons
     */
    createFrontalWindows() {
        const windows = [];

        // Get dimensions of the frontal wall
        const wallWidth = this.frontalWall.width();
        const wallHeight = this.frontalWall.height();

        // Find the bounding box of the wall
        const xValues = this.frontalWall.points.map(p => p.x);
        const yValues = this.frontalWall.points.map(p => p.y);
        const minX = Math.min(...xValues);
        const minY = Math.min(...yValues);

        // Add wall margin - ensure windows don't touch the edges
        const wallMargin = Art.grid_dist * 0.15;

        // Window parameters
        const windowSize = Art.grid_dist * 0.35; // Slightly smaller than door

        // Check if this is a small house (grid width of 2 or less)
        // We can access the original grid width (gridd_w) that was used to construct the house
        // This is more reliable than checking the calculated wall dimensions
        const isSmallHouse = this.width / Art.grid_dist <= 2;

        if (isSmallHouse) {
            // For small houses, create just one window at the bottom
            const bottomWindowX = minX + (wallWidth - windowSize) / 2;
            // Position the window in the lower part of the wall (similar to the bottom windows in larger houses)
            const bottomWindowY = minY + wallHeight * 0.65;

            const bottomWindow = new HouseWindow([
                new Point(bottomWindowX, bottomWindowY),
                new Point(bottomWindowX + windowSize, bottomWindowY),
                new Point(bottomWindowX + windowSize, bottomWindowY + windowSize),
                new Point(bottomWindowX, bottomWindowY + windowSize)
            ]);

            windows.push(bottomWindow);
        } else {
            // For larger houses, create the pattern of 3 windows (1 on top, 2 on bottom)

            // Check if this is a very large house (grid width > 3)
            const isVeryLargeHouse = this.width / Art.grid_dist > 3;

            // Adjust window size based on house size
            let topWindowSize = windowSize;
            let bottomWindowSize = windowSize;

            // For very large houses, make bottom windows 20% bigger
            if (isVeryLargeHouse) {
                bottomWindowSize = windowSize * 1.2;
            }

            // Adjust top window position based on house size
            // For very large houses, move the top window slightly lower
            const topWindowY = minY + wallHeight * (isVeryLargeHouse ? 0.35 : 0.25);

            // Create the top window (centered horizontally)
            const topWindowX = minX + (wallWidth - topWindowSize) / 2;

            const topWindow = new HouseWindow([
                new Point(topWindowX, topWindowY),
                new Point(topWindowX + topWindowSize, topWindowY),
                new Point(topWindowX + topWindowSize, topWindowY + topWindowSize),
                new Point(topWindowX, topWindowY + topWindowSize)
            ]);

            windows.push(topWindow);

            // Create the two bottom windows (evenly spaced in the lower third of the wall)
            const bottomWindowY = minY + wallHeight * 0.65; // Position in the lower part

            // Calculate positions for the two bottom windows
            const leftWindowX = minX + wallWidth * 0.25 - bottomWindowSize / 2;
            const rightWindowX = minX + wallWidth * 0.75 - bottomWindowSize / 2;

            // Create left bottom window
            const leftBottomWindow = new HouseWindow([
                new Point(leftWindowX, bottomWindowY),
                new Point(leftWindowX + bottomWindowSize, bottomWindowY),
                new Point(leftWindowX + bottomWindowSize, bottomWindowY + bottomWindowSize),
                new Point(leftWindowX, bottomWindowY + bottomWindowSize)
            ]);

            // Create right bottom window
            const rightBottomWindow = new HouseWindow([
                new Point(rightWindowX, bottomWindowY),
                new Point(rightWindowX + bottomWindowSize, bottomWindowY),
                new Point(rightWindowX + bottomWindowSize, bottomWindowY + bottomWindowSize),
                new Point(rightWindowX, bottomWindowY + bottomWindowSize)
            ]);

            windows.push(leftBottomWindow, rightBottomWindow);
        }

        return windows;
    }

        createWindowsForWall(wall) {
        const windows = [];

        // Get dimensions of the wall
        const wallWidth = wall.width();
        const wallHeight = wall.height();

        // Find the bounding box of the wall
        const xValues = wall.points.map(p => p.x);
        const yValues = wall.points.map(p => p.y);
        const minX = Math.min(...xValues);
        const minY = Math.min(...yValues);

        // Add wall margin - ensure windows don't touch the edges
        const wallMargin = Art.grid_dist * 0.15;

        // Adjusted dimensions accounting for margins
        const availableWidth = wallWidth - (wallMargin * 2);
        const availableHeight = wallHeight - (wallMargin * 2);

        // Window parameters
        const windowSize = Art.grid_dist * 0.35; // Slightly smaller
        const windowSpacing = Art.grid_dist * 0.25;

        // Calculate how many rows and columns of windows to create
        // For houses with 2x2 dimensions, we'll aim for a single row of windows
        const numRows = Math.max(1, Math.min(2, Math.floor(availableHeight / (windowSize + windowSpacing))));
        const maxColumns = Math.floor(availableWidth / (windowSize + windowSpacing));
        const numColumns = Math.min(maxColumns, Math.max(1, Math.floor(wallWidth / Art.grid_dist) + 1));

        // Calculate total width needed for all windows in a row
        const totalWindowWidth = numColumns * windowSize + (numColumns - 1) * windowSpacing;

        // Calculate horizontal starting position to center the windows
        const startX = minX + wallMargin + (availableWidth - totalWindowWidth) / 2;

        // Calculate vertical positions for the rows (evenly distributed)
        let rowPositions = [];
        if (numRows === 1) {
            // Single row centered vertically
            rowPositions.push(minY + wallMargin + (availableHeight - windowSize) / 2);
        } else {
            // Multiple rows with even spacing
            const rowSpacing = (availableHeight - (numRows * windowSize)) / (numRows + 1);
            for (let row = 0; row < numRows; row++) {
                rowPositions.push(minY + wallMargin + rowSpacing + row * (windowSize + rowSpacing));
            }
        }

        // Loop through rows and columns to create windows
        for (let row = 0; row < numRows; row++) {
            const y = rowPositions[row];

            for (let col = 0; col < numColumns; col++) {
                const x = startX + col * (windowSize + windowSpacing);

                // Create a potential window
                let windowCorners;
                if (this.type == "church") {
                    // Narrower windows for churches
                    const churchWindowWidth = windowSize * 0.33;
                    const xOffset = (windowSize - churchWindowWidth) / 2;
                    windowCorners = [
                        new Point(x + xOffset, y),
                        new Point(x + xOffset + churchWindowWidth, y),
                        new Point(x + xOffset + churchWindowWidth, y + windowSize),
                        new Point(x + xOffset, y + windowSize)
                    ];
                } else {
                    windowCorners = [
                        new Point(x, y),
                        new Point(x + windowSize, y),
                        new Point(x + windowSize, y + windowSize),
                        new Point(x, y + windowSize)
                    ];
                }

                // Check if window is inside the wall polygon with some buffer
                const isInside = this.isWindowInsideWall(windowCorners, wall.points);

                // Check if window overlaps with any door in the house
                const overlapsWithDoor = this.isWindowOverlappingDoor(windowCorners);

                // Only create the window if it's inside the wall and doesn't overlap with a door
                if (isInside && !overlapsWithDoor) {
                    const window = new HouseWindow(windowCorners);
                    windows.push(window);
                }
            }
        }

        return windows;
    }

    isWindowInsideWall(windowCorners, wallPoints) {
        // Create a temporary polygon from the wall points
        const wallPolygon = new Polygon(wallPoints);

        // Check if all corners of the window are inside the wall polygon
        for (const corner of windowCorners) {
            if (!wallPolygon.containsPoint(corner)) {
                return false;
            }
        }
        return true;
    }

    isWindowOverlappingDoor(windowCorners) {
        // If the house doesn't have a door, there can't be any overlap
        if (!this.hasDoor) {
            return false;
        }

        // Find the bounding box of the window
        const windowMinX = Math.min(...windowCorners.map(p => p.x));
        const windowMaxX = Math.max(...windowCorners.map(p => p.x));
        const windowMinY = Math.min(...windowCorners.map(p => p.y));
        const windowMaxY = Math.max(...windowCorners.map(p => p.y));

        // Door parameters (based on createDoor method)
        const w = this.frontalWall.width();
        const doorWidth = Art.grid_dist * 0.4 + Art.rand() * 0.2; // Match the createDoor method
        const doorHeight = Art.grid_dist * 0.7; // Match the createDoor method
        const doorX = this.point.x + w/2 - doorWidth/2;
        const doorY = this.point.y - doorHeight;

        // Check for overlap with door
        return (
            windowMaxX > doorX &&
            windowMinX < doorX + doorWidth &&
            windowMaxY > doorY &&
            windowMinY < doorY + doorHeight
        );
    }

    // Helper method to check if a point is inside a polygon
    //todo separar
    // This method is now in Polygon class as containsPoint

    createDoor() {
        // Use grid dimensions instead of percentages of the wall
        const doorWidth = Art.grid_dist * 0.4+Art.rand()*0.2;  // Door width based on grid
        const doorHeight = Art.grid_dist * 0.7; // Door height based on grid

        // Center the door horizontally on the frontal wall
        let w = this.frontalWall.width();
        const doorX = this.point.x + w/2 - doorWidth/2;

        // Door starts from the bottom
        const doorY = this.point.y - doorHeight;

        let door=new HouseDoor([

            new Point(doorX, doorY),
            new Point(doorX + doorWidth, doorY),
            new Point(doorX + doorWidth, doorY + doorHeight),
            new Point(doorX, doorY + doorHeight)
        ]);
        door.style.fillStyle = this.color;
        door.style.strokeStyle = "#000000"; // Black stroke
        return door;
    }

    // createFills remains the same

    createFills() {
        //corregir perque nomes es fa una vegada

    }



    paint(){

        this.createFills();

        if(this.isFlipped){
         this.frontalWall.style.fillStyle = this.frontalWallFill;
        }else{
            this.frontalWall.style.fillStyle = this.frontalWallFill;
           // this.frontalWall.style.fillStyle = this.lateralWallFill;
        }

        if(this.lateralWallFill!=null){
            this.lateralWall.style.fillStyle = this.lateralWallFill;
        }else{
            this.lateralWall.style.fillStyle = this.wallFill;
        }

        if (this.polygons.length > 0) {
            // Calculate proportional line width based on layer's grid size
            const lineWidth = this.currentGridDist * 0.08*Art.ratio;

            // Set line widths for all polygons
            for (let i = 0; i < this.polygons.length; i++) {
                // Determine which polygon this is (roof, walls, door)
                if (this.polygons[i].zIndex === 2) {
                    // This is the roof
                    if(window.roof_color==undefined){
                        window.roof_color=Art.palette.getRandColor("roof");
                    }
                    let roof_color = window.roof_color

                    if (this.isFlipped) {
                        roof_color = Art.palette.getColorDarkerEx(window.roof_color, this.sunIntensity*2);

                    }

                    this.polygons[i].polygon.setStyle({
                        fillStyle: roof_color,
                        strokeStyle: "#000",
                        lineWidth: lineWidth
                    });
                } else {
                    // For walls and door, just set the line width
                    this.polygons[i].polygon.style.strokeStyle = "#000";
                    this.polygons[i].polygon.style.lineWidth = lineWidth;
                }
            }

       
        }


        // Then call the parent paint method
        super.paint();
        this.paintFrontalWall();

        // Draw a base line
        Art.setStyle({
            strokeStyle: "#000000",
            lineWidth: this.currentGridDist * 0.02
        });
        Art.ctx.beginPath();
        Art.moveTo(this.point.x-20, this.point.y);
        Art.lineTo(this.point.x + 20 +this.width, this.point.y);
        Art.ctx.stroke();


    }
    paintFrontalWall(){
        //nothing here

    }

    clone() {
        // Get bounds using the MultiPolygon's getBounds method
        const bounds = this.getBounds();

        // Calculate the original width and height
        const originalWidth = bounds.width;
        const originalHeight = this.point.y - bounds.minY; // Because point is at bottom

        // Create a new House with the correct dimensions
        const house = new this.constructor(
            new Point(this.point.x, this.point.y),
            originalWidth/Art.grid_dist,
            originalHeight/Art.grid_dist,
            this.type
        );

        // If the original house was flipped, flip the cloned house too
        if (this.isFlipped) {
            house.flipHorizontal();
        }

        // Copy over any other properties if needed
        house.wallFill = this.wallFill;
        if (this.lateralWallFill) {
            house.lateralWallFill = this.lateralWallFill;
        }

        return house;
    }
}


export class HouseWindow extends Polygon {
    constructor(corners) {
        super(corners);
        // No need for type parameter as it's not used
        this.width = Math.max(
            Math.abs(this.points[1].x - this.points[0].x),
            Math.abs(this.points[2].x - this.points[3].x)
        );
        this.height = Math.max(
            Math.abs(this.points[3].y - this.points[0].y),
            Math.abs(this.points[2].y - this.points[1].y)
        );

        // Store current grid distance
        this.currentGridDist = Art.grid_dist;
        // Calculate frame width proportionally
        this.frameWidth = this.currentGridDist * 0.0625; // 4/64 = 0.0625
        this.baseSubdivisionSize = Art.grid_dist / 6;

        // Calculate number of subdivisions
        // Check if this is a larger window (20% larger than standard)
        const isLargerWindow = this.width > Art.grid_dist * 0.35 * 1.1; // 10% threshold to detect larger windows

        if (isLargerWindow) {
            // For larger windows in frontal walls, use 2x2 panes
            this.numPanesX = 2;
            this.numPanesY = 2;
        } else {
            // For standard windows, calculate based on size
            this.numPanesX = Math.max(1, Math.round(this.width / this.baseSubdivisionSize));
            this.numPanesY = Math.max(1, Math.round(this.height / this.baseSubdivisionSize));
        }
    }

    paint() {
        // First fill all panes with black
        Art.setStyle({
            fillStyle: "black"
        });

        for (let y = 0; y < this.numPanesY; y++) {
            for (let x = 0; x < this.numPanesX; x++) {
                const topLeft = this.interpolateRect(x, y);
                const topRight = this.interpolateRect(x + 1, y);
                const bottomLeft = this.interpolateRect(x, y + 1);
                const bottomRight = this.interpolateRect(x + 1, y + 1);

                // Fill the pane
                Art.ctx.beginPath();
                Art.moveTo(topLeft.x, topLeft.y);
                Art.lineTo(topRight.x, topRight.y);
                Art.lineTo(bottomRight.x, bottomRight.y);
                Art.lineTo(bottomLeft.x, bottomLeft.y);
                Art.ctx.closePath();
                Art.ctx.fill();
            }
        }

        // Then draw all white frames
        Art.setStyle({
            strokeStyle: "white",
            lineWidth: this.frameWidth*Art.ratio
        });

        // Draw outer frame
        Art.ctx.beginPath();
        Art.moveTo(this.points[0].x, this.points[0].y);
        for (let i = 1; i < this.points.length; i++) {
            Art.lineTo(this.points[i].x, this.points[i].y);
        }
        Art.ctx.closePath();
        Art.ctx.stroke();

        // Draw vertical dividers
        for (let i = 1; i < this.numPanesX; i++) {
            const ratio = i / this.numPanesX;
            const startPoint = this.points[0].getMiddle(this.points[1], ratio);
            const endPoint = this.points[3].getMiddle(this.points[2], ratio);

            Art.ctx.beginPath();
            Art.moveTo(startPoint.x, startPoint.y);
            Art.lineTo(endPoint.x, endPoint.y);
            Art.ctx.stroke();
        }

        // Draw horizontal dividers
        for (let i = 1; i < this.numPanesY; i++) {
            const ratio = i / this.numPanesY;
            const startPoint = this.points[0].getMiddle(this.points[3], ratio);
            const endPoint = this.points[1].getMiddle(this.points[2], ratio);

            Art.ctx.beginPath();
            Art.moveTo(startPoint.x, startPoint.y);
            Art.lineTo(endPoint.x, endPoint.y);
            Art.ctx.stroke();
        }

        Art.restoreStyle();
    }

    // Update interpolateRect to use getMiddle
    //todo change
    interpolateRect(x, y) {
        const xRatio = x / this.numPanesX;
        const yRatio = y / this.numPanesY;

        const topPoint = this.points[0].getMiddle(this.points[1], xRatio);
        const bottomPoint = this.points[3].getMiddle(this.points[2], xRatio);
        return topPoint.getMiddle(bottomPoint, yRatio);
    }

    // This method is now in the Polygon class
}


export class HouseDoor extends Polygon {
    constructor(corners) {
        super(corners);
        // Store current grid distance
        this.currentGridDist = Art.grid_dist;
        // Calculate frame width proportionally
        this.frameWidth = this.currentGridDist * 0.0625; // 4/64 = 0.0625
    }

    paint() {
        // First paint the door fill
        Art.setStyle({
            fillStyle: this.style.fillStyle
        });

        Art.ctx.beginPath();
        Art.moveTo(this.points[0].x, this.points[0].y);
        for (let i = 1; i < this.points.length; i++) {
            Art.lineTo(this.points[i].x, this.points[i].y);
        }
        Art.ctx.closePath();
        Art.ctx.fill();

        // Then draw white frame (only top and sides)
        Art.setStyle({
            strokeStyle: "white",
            lineWidth: this.frameWidth*Art.ratio
        });

        // Draw frame in a single continuous path
        Art.ctx.beginPath();
        // Start from bottom-left
        Art.moveTo(this.points[3].x, this.points[3].y);
        // Draw up to top-left
        Art.lineTo(this.points[0].x, this.points[0].y);
        // Draw to top-right
        Art.lineTo(this.points[1].x, this.points[1].y);
        // Draw down to bottom-right
        Art.lineTo(this.points[2].x, this.points[2].y);

        Art.ctx.stroke();
        Art.restoreStyle();
    }
}
