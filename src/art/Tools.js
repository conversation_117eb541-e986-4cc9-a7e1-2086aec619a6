import {Art} from './Art.js';

export class Tools {
    
    static getRandFromArray(arr){
        return arr[Math.floor(Art.rand() * arr.length)];    
    }

    static randInt(num){
        return Math.floor(Art.rand()*num)
    }


    //pattern like {1:0.5,2:0.5,3:0.5}
    static randPattern(pattern,num) {
        // Remove all values from the pattern that are greater than num
        for (const key in pattern) {
            if (key > num) {
                delete pattern[key];
            }
        }

        // Get total probability sum (in case they don't add up to 1)
        const total = Object.values(pattern).reduce((sum, probability) => sum + probability, 0);
        
        // Generate random value between 0 and total
        const rand = Art.rand() * total;
        
        // Iterate through keys and find which range our random value falls into
        let cumulativeProbability = 0;
        for (const key in pattern) {
            cumulativeProbability += pattern[key];
            if (rand <= cumulativeProbability) {
                return key;
            }
        }
        
        // Fallback - should rarely get here unless there's a floating point precision issue
        return Object.keys(pattern)[Object.keys(pattern).length - 1];
    }

    // Adjust a color's brightness (positive values make it lighter, negative make it darker)
    static adjustColorBrightness(hexColor, factor) {
        // Parse hex color to RGB
        const r = parseInt(hexColor.slice(1, 3), 16);
        const g = parseInt(hexColor.slice(3, 5), 16);
        const b = parseInt(hexColor.slice(5, 7), 16);
        
        // Adjust the brightness
        let newR, newG, newB;
        if (factor < 0) {
            // Darken
            newR = Math.max(0, Math.round(r * (1 + factor)));
            newG = Math.max(0, Math.round(g * (1 + factor)));
            newB = Math.max(0, Math.round(b * (1 + factor)));
        } else {
            // Lighten
            newR = Math.min(255, Math.round(r + (255 - r) * factor));
            newG = Math.min(255, Math.round(g + (255 - g) * factor));
            newB = Math.min(255, Math.round(b + (255 - b) * factor));
        }
        
        // Convert back to hex
        return '#' + 
            newR.toString(16).padStart(2, '0') + 
            newG.toString(16).padStart(2, '0') + 
            newB.toString(16).padStart(2, '0');
    }
}
