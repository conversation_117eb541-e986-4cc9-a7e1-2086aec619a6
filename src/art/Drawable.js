// Base drawable class
import { Style } from './Style.js';
import { Art } from './Art.js';

export  class Drawable {
    isFlipped = false;

    constructor() {
      this.style = new Style();
      this.style.strokeStyle = Art.style.strokeStyle
      this.style.fillStyle = Art.style.fillStyle
      this.style.lineWidth = Art.style.lineWidth
      this.style.lineCap = Art.style.lineCap
      this.style.lineJoin = Art.style.lineJoin

      this.zIndex = 0;
    }

    setStyle(style) {
      if (!style) return this;

      // Only update properties that exist in the passed style object
      Object.keys(style).forEach(key => {
        if (this.style.hasOwnProperty(key)) {
          this.style[key] = style[key];
        }
      });

      this.style.apply();
      return this;
    }

    getBounds() {
      // Default implementation for general drawables
      return {
        minX: 0,
        minY: 0,
        maxX: Art.width || 800,
        maxY: Art.height || 600,
        width: Art.width || 800,
        height: Art.height || 600
      };
    }

    // Default paint method that does nothing
    // This ensures all Drawable objects have a paint method
    paint() {
      // Default implementation does nothing
      // Subclasses should override this method
    }

  }


