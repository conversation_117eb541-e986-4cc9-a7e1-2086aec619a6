// Class for handling landscape layers with contour lines
import { Drawable } from './Drawable.js';
import { Art } from './Art.js';
import { Point } from './Point.js';
import { Polygon } from './Polygon.js';

export class ContourLayer extends Drawable {
    constructor(points = []) {
        super();

        // Create an internal polygon for containment checks and other operations
        this.polygon = new Polygon(points);

        // Copy points from the polygon
        this.points = this.polygon.points;

        // Default properties
        this.grid_dist = Art.grid_dist;
        this.margin = 0;
        this.spots = [];
        this.contourSpacing = 20; // Default spacing between contour lines (in pixels)
    }

    // Delegate containsPoint to the internal polygon
    containsPoint(point) {
        return this.polygon.containsPoint(point);
    }

    // Delegate width calculation to the internal polygon
    width() {
        return this.polygon.width();
    }

    // Delegate height calculation to the internal polygon
    height() {
        return this.polygon.height();
    }

    // Delegate getYAtX to the internal polygon
    getYAtX(x) {
        return this.polygon.getYAtX(x);
    }

    // Add a point to the layer
    addPoint(point) {
        this.points.push(point);
        this.polygon.addPoint(point);
        return this;
    }

    // Set the style for the layer
    setStyle(style) {
        this.style = style;
        this.polygon.setStyle(style);
        return this;
    }

    // Paint the layer with parallel contour lines
    paint() {
        // Apply the style
        this.style.apply();

        // First, fill the polygon with the base color
        this.polygon.fill();

        // Then draw the contour lines
        //this.drawContourLines();

        // Finally, draw the outline
        this.polygon.stroke();
    }

    // Draw parallel contour lines along the edges of the polygon
    drawContourLines() {
        // Save the current context state
        Art.ctx.save();

        // Set the style for contour lines
        Art.ctx.strokeStyle = "#000000";
        // Make line width proportional to grid_dist for better scaling with perspective
        // Increase the multiplier to make lines more visible in nearer layers
        Art.ctx.lineWidth = (this.grid_dist * 0.01) * Art.ratio;

        // Find the top edge of the landscape (the mountain profile)
        const topEdge = this.findTopEdge();

        // Draw contour lines parallel to the top edge
        this.drawParallelContours(topEdge);

        // Restore the context state
        Art.ctx.restore();
    }

    // Find the top edge of the landscape (the mountain profile)
    findTopEdge() {
        const points = this.points;
        const topEdge = [];

        // Find the leftmost and rightmost x-coordinates
        const xValues = points.map(p => p.x);
        const minX = Math.min(...xValues);
        const maxX = Math.max(...xValues);

        // Find the top points that form the mountain profile
        // We'll look for points where y is decreasing (going up) then increasing (going down)
        let topPoints = [];

        // First, find all points that could be part of the top edge
        for (let i = 0; i < points.length; i++) {
            const p = points[i];
            const nextP = points[(i + 1) % points.length];

            // If this segment is mostly horizontal and near the top of the polygon,
            // it's likely part of the top edge
            if (Math.abs(nextP.y - p.y) < Math.abs(nextP.x - p.x) * 0.5) {
                // Check if this point is in the top half of the polygon
                const bounds = this.getBounds();
                const midY = (bounds.minY + bounds.maxY) / 2;

                if (p.y < midY && nextP.y < midY) {
                    topPoints.push(p);
                    // Don't add nextP here to avoid duplicates
                }
            }
        }

        // Sort points by x-coordinate
        topPoints.sort((a, b) => a.x - b.x);

        // Always use the sampling approach to ensure consistent coverage
        // Use more segments for distant mountains (smaller grid_dist)
        const segmentCount = Math.max(40, Math.floor(100 / (this.grid_dist / 50)));
        const step = (maxX - minX) / segmentCount;

        // Ensure we start slightly before the left edge and end slightly after the right edge
        // This helps with coverage on the edges
        const startX = minX - step;
        const endX = maxX + step;

        for (let x = startX; x <= endX; x += step) {
            let minY = Infinity;
            let topPoint = null;

            // Find the highest point (minimum y) at this x-coordinate
            for (let i = 0; i < points.length; i++) {
                const p1 = points[i];
                const p2 = points[(i + 1) % points.length];

                // Skip vertical edges
                if (p1.x === p2.x) continue;

                // Check if x is within this edge
                if ((x >= p1.x && x <= p2.x) || (x >= p2.x && x <= p1.x)) {
                    // Calculate the y-coordinate at this x
                    const t = (x - p1.x) / (p2.x - p1.x);
                    const y = p1.y + t * (p2.y - p1.y);

                    if (y < minY) {
                        minY = y;
                        topPoint = new Point(x, y);
                    }
                }
            }

            if (topPoint) {
                topEdge.push(topPoint);
            }
        }

        // If we still don't have enough points, add the original topPoints as a fallback
        if (topEdge.length < 2 && topPoints.length >= 2) {
            topEdge.push(...topPoints);
        }

        return topEdge;
    }

    // Draw contour lines parallel to the top edge
    drawParallelContours(topEdge) {
        if (topEdge.length < 2) return; // Need at least 2 points to draw lines

        // Get the bounds of the polygon
        const bounds = this.getBounds();

        // Use $fx parameter to control contour density if available
        let densityFactor = 1.0;
        try {
            densityFactor = $fx.getParam("contour_density");
        } catch (e) {
            // If $fx is not available or parameter is not defined, use default
            console.log("Using default contour density");
        }

        // Define minimum spacing between contour lines (in pixels)
        const minSpacingPx = 5;

        // Convert to logical units based on Art.ratio
        const minSpacing = minSpacingPx / Art.ratio;

        // Apply density factor to contour spacing (smaller spacing = more lines)
        const adjustedSpacing = this.contourSpacing / densityFactor;

        // Calculate actual spacing to use (max of minSpacing and adjustedSpacing)
        const actualSpacing = Math.max(adjustedSpacing, minSpacing);

        // Recalculate number of contours based on actual spacing
        const actualMaxContours = Math.floor((bounds.maxY - bounds.minY) / actualSpacing);

        // Draw each contour line
        for (let i = 1; i <= actualMaxContours; i++) {
            const offset = i * actualSpacing;

            // Create a path for this contour
            Art.ctx.beginPath();

            // Start at the first point
            let started = false;
            let lastValidX = null;
            let lastValidY = null;
            let segmentPoints = [];

            // First pass: collect all valid points for this contour
            for (let j = 0; j < topEdge.length; j++) {
                const p = topEdge[j];
                const x = p.x;
                const y = p.y + offset; // Offset downward

                // Skip if the point is outside the polygon's vertical bounds
                if (y > bounds.maxY) continue;

                // Check if this point is inside the polygon
                const point = new Point(x, y);
                if (this.containsPoint(point)) {
                    segmentPoints.push(point);
                }
            }

            // Second pass: identify and draw continuous segments
            if (segmentPoints.length > 0) {
                // Sort points by x-coordinate to ensure proper ordering
                segmentPoints.sort((a, b) => a.x - b.x);

                // Find continuous segments
                let currentSegment = [segmentPoints[0]];

                for (let j = 1; j < segmentPoints.length; j++) {
                    const currentPoint = segmentPoints[j];
                    const prevPoint = segmentPoints[j - 1];

                    // Check if this point is part of the current segment
                    // Use a threshold based on grid_dist to determine continuity
                    // Increase the threshold for better connectivity in all layers
                    const threshold = this.grid_dist * 1.0;
                    if (currentPoint.x - prevPoint.x <= threshold) {
                        // Points are close enough, add to current segment
                        currentSegment.push(currentPoint);
                    } else {
                        // Gap detected, draw the current segment and start a new one
                        if (currentSegment.length > 1) {
                            // Draw this segment
                            Art.ctx.beginPath();
                            Art.moveTo(currentSegment[0].x * Art.ratio, currentSegment[0].y * Art.ratio);

                            for (let k = 1; k < currentSegment.length; k++) {
                                Art.lineTo(currentSegment[k].x * Art.ratio, currentSegment[k].y * Art.ratio);
                            }

                            Art.ctx.stroke();
                        }

                        // Start a new segment
                        currentSegment = [currentPoint];
                    }
                }

                // Draw the last segment
                if (currentSegment.length > 1) {
                    Art.ctx.beginPath();
                    Art.moveTo(currentSegment[0].x * Art.ratio, currentSegment[0].y * Art.ratio);

                    for (let k = 1; k < currentSegment.length; k++) {
                        Art.lineTo(currentSegment[k].x * Art.ratio, currentSegment[k].y * Art.ratio);
                    }

                    Art.ctx.stroke();
                }
            }
        }
    }

    // Get the bounds of the polygon
    getBounds() {
        return this.polygon.getBounds();
    }

    // Stroke the outline of the polygon
    stroke() {
        this.polygon.stroke();
        return this;
    }

    // Fill the polygon
    fill() {
        this.polygon.fill();
        return this;
    }

    // Trace the polygon path without filling or stroking
    trace() {
        this.polygon.trace();
        return this;
    }
}
