import { Polygon } from './Polygon.js';
import { Point } from './Point.js';
import { Art } from './Art.js';

export class Rectangle extends Polygon {
    constructor(x, y, width, height) {
        const points = [];
        const step = Art.dist;

        // Top edge
        for (let i = 0; i <= width; i += step) {
            points.push(new Point(x + i, y));
        }
        // Right edge
        for (let i = 0; i <= height; i += step) {
            points.push(new Point(x + width, y + i));
        }
        // Bottom edge
        for (let i = width; i >= 0; i -= step) {
            points.push(new Point(x + i, y + height));
        }
        // Left edge
        for (let i = height; i >= 0; i -= step) {
            points.push(new Point(x, y + i));
        }

        super(points);
    }
}
export class Ellipse extends Polygon {
    constructor(cx, cy, rx, ry,step=Art.dist) {
        const points = [];
       // const step = Art.dist;
        const circumference = 2 * Math.PI * Math.max(rx, ry);
        const numPoints = Math.ceil(circumference / step);

        for (let i = 0; i < numPoints; i++) {
            const angle = (i / numPoints) * 2 * Math.PI;
            points.push(new Point(
                cx + rx * Math.cos(angle),
                cy + ry * Math.sin(angle)
            ));
        }

        super(points);
    }
}

export class Triangle extends Polygon {
    constructor(p1, p2, p3) {
        const points = [];
        const step = Art.dist;

        // Edge from p1 to p2
        const dist1 = Math.hypot(p2.x - p1.x, p2.y - p1.y);
        const steps1 = Math.ceil(dist1 / step);
        for (let i = 0; i <= steps1; i++) {
            const t = i / steps1;
            points.push(new Point(p1.x + t * (p2.x - p1.x), p1.y + t * (p2.y - p1.y)));
        }

        // Edge from p2 to p3
        const dist2 = Math.hypot(p3.x - p2.x, p3.y - p2.y);
        const steps2 = Math.ceil(dist2 / step);
        for (let i = 0; i <= steps2; i++) {
            const t = i / steps2;
            points.push(new Point(p2.x + t * (p3.x - p2.x), p2.y + t * (p3.y - p2.y)));
        }

        // Edge from p3 to p1
        const dist3 = Math.hypot(p1.x - p3.x, p1.y - p3.y);
        const steps3 = Math.ceil(dist3 / step);
        for (let i = 0; i <= steps3; i++) {
            const t = i / steps3;
            points.push(new Point(p3.x + t * (p1.x - p3.x), p3.y + t * (p1.y - p3.y)));
        }

        super(points);
    }
}