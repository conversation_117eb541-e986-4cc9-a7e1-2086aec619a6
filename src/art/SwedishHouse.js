import { Drawable } from './Drawable.js';
import { Point } from './Point.js';
import { Art } from './Art.js';
import { MultiPolygon, Polygon } from './Polygon.js';
import { House } from './House.js';
import { Ellipse } from './PolygonForms.js';
import { Tools } from './Tools.js';

export class SwedishHouse extends House {
    constructor(point, width, height, type="house") {
        super(point, width, height, type);

        // Randomly decide whether to add a chimney (10% chance)
        this.hasChimney = Art.rand() < 0.1;
    }


    /**
     * Desaturate a color by a given amount
     * @param {string} color - The color to desaturate in hex format (#RRGGBB)
     * @param {number} amount - Amount to desaturate (0-1)
     * @returns {string} - The desaturated color in hex format
     */
    desaturateColor(color, amount) {
        // Convert hex to RGB
        const r = parseInt(color.substring(1, 3), 16) / 255;
        const g = parseInt(color.substring(3, 5), 16) / 255;
        const b = parseInt(color.substring(5, 7), 16) / 255;

        // Calculate luminance (perceived brightness)
        const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

        // Interpolate between original color and grayscale based on amount
        const newR = r + amount * (luminance - r);
        const newG = g + amount * (luminance - g);
        const newB = b + amount * (luminance - b);

        // Convert back to hex
        const toHex = (c) => {
            const hex = Math.round(c * 255).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };

        return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
    }

    static getHouse(w, p, layerIndex){ // de 2 a 6

    // Choose a random height based on width constraints
    let possibleHeights;

    // Set possible heights based on width (w)
    switch (w) {
        case 2:
            possibleHeights = [2, 3];
            break;
        case 3:
            possibleHeights = [2, 3];
            break;
        case 4:
            possibleHeights = [2, 3, 4];
            break;
        case 5:
            possibleHeights = [2, 3, 4];
            break;
        case 6:
            possibleHeights = [2, 3, 4, 5];
            break;
        case 7:
            possibleHeights = [2, 3, 4, 5];
            break;
        case 8:
            possibleHeights = [3, 4, 5, 6];
            break;
        default:
            // For any other width, use a reasonable range
            possibleHeights = [Math.max(2, Math.floor(w * 0.5)), Math.min(6, Math.ceil(w * 0.75))];
    }

    // Choose a random height from the possible range
    const h = Tools.getRandFromArray(possibleHeights);

        //if(h>w*2) h=w*1.25
        const house = new SwedishHouse(
            p,
            w,
            h
        );

        // Store the layer index for desaturation in createFills
        if (layerIndex !== undefined) {
            house.layerIndex = layerIndex;
        }

        if(Art.rand()<0.5){
            house.flipHorizontal();
        }
        return house;

    }


    // Create striped pattern fills for the walls
    createFills() {
        // Get a base color for the house
        let baseColor = Art.palette.getRandColor("house", this.point.x, this.point.y, this.currentGridDist);

        // Adjust saturation based on layer index (if provided)
        if (this.layerIndex !== undefined) {
            // Calculate desaturation factor based on layer index
            // Further layers (higher index) get more desaturated
            // Layer 0 is closest, higher indices are further away
            const desaturationFactor = this.layerIndex * 0.1; // 15% desaturation per layer

            // Desaturate the base color
            baseColor = this.desaturateColor(baseColor, desaturationFactor);
        }

        // Create a more subtle striped pattern for the walls
        // Use this.currentGridDist instead of Art.grid_dist to ensure proper scaling with house size
        const stripeWidth = this.currentGridDist * 0.2; // Width proportional to the house's grid distance
        const stripeHeight = this.currentGridDist * 0.4; // Height proportional to the house's grid distance

        // Create a canvas for the pattern
        const canvas = document.createElement('canvas');

        // Ensure the canvas dimensions are valid (at least 1 pixel)
        const canvasWidth = Math.max(1, stripeWidth * 2 * Art.ratio);
        const canvasHeight = Math.max(1, stripeHeight * Art.ratio);

        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        // Get the canvas context
        const ctx = canvas.getContext('2d');

        // Save the current Art context and set it to our pattern canvas
        Art.setCtx(ctx);

        // Create an extremely subtle darker color for alternating stripes (minimal contrast)
        let darkerColor = Art.palette.getColorDarkerEx(baseColor, 0.04); // Very minimal darkening for subtle effect

        // Draw the first stripe (base color)
        ctx.fillStyle = baseColor;
        Art.fillRect(0, 0, stripeWidth, stripeHeight);

        // Draw the second stripe (slightly darker color)
        ctx.fillStyle = darkerColor;
        Art.fillRect(stripeWidth, 0, stripeWidth, stripeHeight);

        // Make the dividing line between stripes extremely subtle
        ctx.strokeStyle = Art.palette.getColorDarkerEx(baseColor, 0.05); // Very slight darkening
        ctx.lineWidth = 0.3 * Art.ratio; // Extremely thin line
        ctx.globalAlpha = 0.15; // Very low opacity for maximum subtlety
        ctx.beginPath();
        Art.moveTo(stripeWidth, 0);
        Art.lineTo(stripeWidth, stripeHeight);
        ctx.stroke();
        ctx.globalAlpha = 1.0; // Reset alpha

        // Restore the original Art context
        Art.restoreCtx();

        // Create the pattern from the canvas
        this.wallFill = ctx.createPattern(canvas, 'repeat');

        // Create adjusted versions for frontal and lateral walls based on lighting
        if (this.isFlipped) {
            // When flipped, the frontal wall gets more light, lateral wall gets less
            this.frontalWallFill = this.wallFill; // Use pattern directly for frontal wall

            // For the lateral wall, create a darker version of the pattern
            const lateralCanvas = document.createElement('canvas');
            lateralCanvas.width = Math.max(1, canvas.width);
            lateralCanvas.height = Math.max(1, canvas.height);
            const lateralCtx = lateralCanvas.getContext('2d');

            // Draw the original pattern with a very subtle dark overlay
            lateralCtx.fillStyle = this.wallFill;
            lateralCtx.fillRect(0, 0, lateralCanvas.width, lateralCanvas.height);
            lateralCtx.fillStyle = 'rgba(0, 0, 0, ' + (this.sunIntensity * 0.7) + ')';
            lateralCtx.fillRect(0, 0, lateralCanvas.width, lateralCanvas.height);

            this.lateralWallFill = lateralCtx.createPattern(lateralCanvas, 'repeat');
        } else {
            // When not flipped, the lateral wall gets more light, frontal wall gets less
            this.lateralWallFill = this.wallFill; // Use pattern directly for lateral wall

            // For the frontal wall, create a darker version of the pattern
            const frontalCanvas = document.createElement('canvas');
            frontalCanvas.width = Math.max(1, canvas.width);
            frontalCanvas.height = Math.max(1, canvas.height);
            const frontalCtx = frontalCanvas.getContext('2d');

            // Draw the original pattern with a very subtle dark overlay
            frontalCtx.fillStyle = this.wallFill;
            frontalCtx.fillRect(0, 0, frontalCanvas.width, frontalCanvas.height);
            frontalCtx.fillStyle = 'rgba(0, 0, 0, ' + (this.sunIntensity * 0.7) + ')';
            frontalCtx.fillRect(0, 0, frontalCanvas.width, frontalCanvas.height);

            this.frontalWallFill = frontalCtx.createPattern(frontalCanvas, 'repeat');
        }
    }


    paintFrontalWall(){
        super.paintFrontalWall();





       // frontMargin.paint()
    }

    createMoreStuff(){

        const margin=Art.grid_dist*0.15
        let  p1=this.frontalWall.getPoint(0).move(margin,0)
        let p2=this.frontalWall.getPoint(1).move(0,margin*1.5)
        let p3=this.frontalWall.getPoint(2).move(-margin,0)

        const frontMargin=new Polygon(
            [p1,this.frontalWall.getPoint(0),this.frontalWall.getPoint(1),this.frontalWall.getPoint(2),p3,p2]
        )
        frontMargin.setStyle({
            fillStyle: '#fff',

        });

        this.addPolygon(frontMargin, 1);
    }

}

export class SwedishChurch extends SwedishHouse{
    // Static constants for roof configuration
    static ROOF_BOTTOM_HEIGHT_PROP = 0.15; // 15% of total roof height
    static ROOF_MIDDLE_HEIGHT_PROP = 0.25; // 25% of total roof height
    static ROOF_TOP_HEIGHT_PROP = 0.6;     // 60% of total roof height

    // Static constants for roof colors
    static ROOF_COLOR = "#4A4A4A";         // Dark gray for the main roof
    static TOWER_COLOR = "#333333";        // Darker gray for the tower/spire


    constructor(point, width,type="church") {
        let height=3
        if(width==3) height=2.25
        if(width==4) height=3
        if(width==4 && type=="dom") height=5
        if(width==5) height=3.8
        if(width==6) height=4.6
        super(point, width, height, "church");

        // Ensure currentGridDist is set (needed for createFills)
        this.currentGridDist = Art.grid_dist;

        // Calculate total roof height
        const hw = 0.5; // Base height factor
        const ht = 2; // Total height factor
        const totalRoofHeight = this.height * ht - this.height * hw;

        // Calculate actual heights for each level using static constants
        const bottomHeight = totalRoofHeight * SwedishChurch.ROOF_BOTTOM_HEIGHT_PROP;
        const middleHeight = totalRoofHeight * SwedishChurch.ROOF_MIDDLE_HEIGHT_PROP;
        // Top height is determined by the remaining space

        // Calculate y-coordinates for each level
        const baseY = this.point.y - this.height * hw; // Base of the roof
        const bottomLevelY = baseY - bottomHeight;
        const middleLevelY = bottomLevelY - middleHeight;
        const topLevelY = this.point.y - this.height * ht; // Top of the roof (peak)

        // Calculate x-coordinates
        const leftX = this.point.x;
        const rightX = this.point.x + this.width / 2;
        const peakX = this.point.x + this.width / 4;

        // Calculate intermediate points for the tapered shape
        const bottomWidthFactor = 0.15; // How much the roof narrows at the bottom level

        // Calculate intermediate x-coordinates
        const bottomLeftX = leftX + (peakX - leftX) * bottomWidthFactor;
        const bottomRightX = rightX - (rightX - peakX) * bottomWidthFactor;

        // For vertical middle walls, use the same x-coordinates as the bottom level
        const middleLeftX = bottomLeftX;
        const middleRightX = bottomRightX;

        // For a sharper top, make the top section narrower
        const topWidthFactor = 0.6; // Higher value = sharper top
        const topLeftX = middleLeftX + (peakX - middleLeftX) * topWidthFactor;
        const topRightX = middleRightX - (middleRightX - peakX) * topWidthFactor;

        // Create the bottom level of the roof (tapered)
        const roofBottom = new Polygon([
            new Point(leftX, baseY),
            new Point(bottomLeftX, bottomLevelY),
            new Point(bottomRightX, bottomLevelY),
            new Point(rightX, baseY)
        ]);

        // Create the middle level of the roof (vertical walls)
        const roofMiddle = new Polygon([
            new Point(bottomLeftX, bottomLevelY),
            new Point(middleLeftX, middleLevelY),
            new Point(middleRightX, middleLevelY),
            new Point(bottomRightX, bottomLevelY)
        ]);

        // Create the top level of the roof (sharper peak)
        const roofTop = new Polygon([
            new Point(middleLeftX, middleLevelY),
            new Point(topLeftX, middleLevelY - (middleLevelY - topLevelY) * 0.3),
            new Point(peakX, topLevelY),
            new Point(topRightX, middleLevelY - (middleLevelY - topLevelY) * 0.3),
            new Point(middleRightX, middleLevelY)
        ]);


        // Use our static color constants instead of window.roof_color
        const roofColor = SwedishChurch.ROOF_COLOR;
        const strokeWidth = Art.grid_dist * 0.02;

        // Set styles for each roof level with slightly different shades for visual interest
        roofBottom.setStyle({
            fillStyle: roofColor,
            strokeStyle: "#000000",
            lineWidth: strokeWidth
        });

        // Middle roof - slightly darker for visual separation
        const middleRoofColor =this.color// Art.palette.getColorDarkerEx(roofColor, 0.1);
        roofMiddle.setStyle({
            fillStyle: middleRoofColor,
            strokeStyle: "#000000",
            lineWidth: strokeWidth
        });

        // Top roof - even darker for better visual hierarchy
        const topRoofColor =middleRoofColor// Art.palette.getColorDarkerEx(roofColor, 0.2);
        roofTop.setStyle({
            fillStyle: topRoofColor,
            strokeStyle: "#000000",
            lineWidth: strokeWidth
        });

        // Add the roof levels to the church with appropriate z-indices
        this.addPolygon(roofBottom, 2); // Bottom level
        this.addPolygon(roofMiddle, 3); // Middle level
        this.addPolygon(roofTop, 4);    // Top level (peak)

        // Store references for later use
        this.roofBottom = roofBottom;
        this.roofMiddle = roofMiddle;
        this.roofTop = roofTop;

        // Store the roof colors for use in other methods
        this.roof_color = roofColor; // For compatibility with existing code
        this.tower_color = SwedishChurch.TOWER_COLOR;
        this.cross_color = SwedishChurch.CROSS_COLOR;

        // Calculate and store the clock position (center of the middle roof section)
        this.clockX = (middleLeftX + middleRightX) / 2;
        this.clockY = (bottomLevelY + middleLevelY) / 2;
        // Make the clock half size, and ensure it's never bigger than the middle roof
        this.clockRadius = Math.min(middleHeight, (middleRightX - middleLeftX) / 2) * 0.35; // 35% of the available space (half of 70%)
    }

    paint(){
        // If we have a layer index, make sure to apply it before painting
        if (this.layerIndex !== undefined && !this.fillsCreated) {
            // Ensure the createFills method will use the layer index for desaturation
            // This is needed because SwedishChurch extends SwedishHouse
            try {
                this.createFills();
                this.fillsCreated = true; // Mark fills as created to avoid redundant calls
            } catch (error) {
                console.error('Error creating fills for church:', error);
                // Set default fills to avoid further errors
                this.wallFill = this.frontalWallFill = this.lateralWallFill = '#FFFFFF';
            }
        }

        super.paint();

        // Draw the clock on the middle roof
        this.drawClock();

        // Draw the cross at the top of the church
        this.drawCross();
    }

    // Override the flipHorizontal method to update the clock position
    flipHorizontal() {
        // Call the parent class's flipHorizontal method
        super.flipHorizontal();

        // Update the clock position if it exists
        if (this.clockX !== undefined && this.clockY !== undefined) {
            // Calculate the width of the church
            const width = this.width;

            // Calculate the new clock position
            // When flipped, the clock should be on the opposite side
            // The formula is: newX = 2 * centerX - oldX
            // Where centerX is the center of the church (this.point.x + width/2)
            const centerX = this.point.x + width/2;
            this.clockX = 2 * centerX - this.clockX;
        }
    }

    drawClock() {
        // Check if clock position is defined
        if (!this.clockX || !this.clockY) return;

        // Calculate clock radius - make it smaller (half size, never bigger than the middle roof)
        // Get the width of the middle roof section
        const middleRoofWidth = this.roofMiddle.getBounds().width;
        // Make the clock radius proportional to the middle roof width, but not too large
        this.clockRadius = Math.min(middleRoofWidth * 0.25, this.height * 0.2);

        // Get the current context
        const ctx = Art.ctx;

        // Save current context state
        ctx.save();

        // Create a subtle shadow for depth
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 5;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;

        // Draw clock face - simple circle with a subtle gradient for dimension
        const gradient = ctx.createRadialGradient(
            this.clockX, this.clockY, this.clockRadius * 0.1,
            this.clockX, this.clockY, this.clockRadius
        );
        gradient.addColorStop(0, "#FFFFFF");
        gradient.addColorStop(1, "#F0F0F0");

        ctx.beginPath();
        ctx.arc(this.clockX, this.clockY, this.clockRadius, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();

        // Add a subtle outer ring
        ctx.strokeStyle = "#333333";
        ctx.lineWidth = Art.grid_dist * 0.02;
        ctx.stroke();

        // Remove shadow for the clock details
        ctx.shadowColor = 'transparent';

        // Draw simplified hour markers - just 4 main markers (12, 3, 6, 9)
        ctx.lineWidth = Art.grid_dist * 0.015;
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI / 2); // 0, 90, 180, 270 degrees
            const markerLength = this.clockRadius * 0.15;
            const startRadius = this.clockRadius * 0.75;

            ctx.beginPath();
            ctx.moveTo(
                this.clockX + Math.sin(angle) * startRadius,
                this.clockY - Math.cos(angle) * startRadius
            );
            ctx.lineTo(
                this.clockX + Math.sin(angle) * (startRadius + markerLength),
                this.clockY - Math.cos(angle) * (startRadius + markerLength)
            );
            ctx.stroke();
        }

        // Get current time
        const now = new Date();
        const hours = now.getHours() % 12;
        const minutes = now.getMinutes();

        // Calculate angles for clock hands
        let hourAngle = (hours + minutes / 60) * Math.PI / 6;
        let minuteAngle = minutes * Math.PI / 30;

        // We want both clocks to show the same time, so we don't adjust the angles
        // for flipped churches anymore. This ensures both clocks show the same time.

        // Draw hour hand - shorter and thicker
        ctx.beginPath();
        ctx.moveTo(this.clockX, this.clockY);
        ctx.lineTo(
            this.clockX + Math.sin(hourAngle) * this.clockRadius * 0.5,
            this.clockY - Math.cos(hourAngle) * this.clockRadius * 0.5
        );
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = Art.grid_dist * 0.03;
        ctx.stroke();

        // Draw minute hand - longer and thinner
        ctx.beginPath();
        ctx.moveTo(this.clockX, this.clockY);
        ctx.lineTo(
            this.clockX + Math.sin(minuteAngle) * this.clockRadius * 0.7,
            this.clockY - Math.cos(minuteAngle) * this.clockRadius * 0.7
        );
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = Art.grid_dist * 0.015;
        ctx.stroke();

        // Draw center dot with a metallic look
        const centerGradient = ctx.createRadialGradient(
            this.clockX, this.clockY, 0,
            this.clockX, this.clockY, this.clockRadius * 0.08
        );
        centerGradient.addColorStop(0, "#666666");
        centerGradient.addColorStop(0.5, "#333333");
        centerGradient.addColorStop(1, "#111111");

        ctx.beginPath();
        ctx.arc(this.clockX, this.clockY, this.clockRadius * 0.08, 0, Math.PI * 2);
        ctx.fillStyle = centerGradient;
        ctx.fill();

        // Add a small highlight to the center dot
        ctx.beginPath();
        ctx.arc(
            this.clockX - this.clockRadius * 0.02,
            this.clockY - this.clockRadius * 0.02,
            this.clockRadius * 0.02,
            0, Math.PI * 2
        );
        ctx.fillStyle = "rgba(255, 255, 255, 0.7)";
        ctx.fill();

        // Restore context state
        ctx.restore();
    }

    drawCross() {
        // Calculate the position for the cross (at the peak of the roof)
        let crossX = this.point.x + this.width/4;
        if(this.isFlipped){
            crossX = this.point.x + this.width*(3/4);
        }
        const crossY = this.point.y - this.height*2;

        const crossWidth = this.width * 0.1;
        const crossHeight = this.height * 0.2;
        const circleRadius = crossWidth * 0.3;

        // Use the same roof color that was defined in the constructor

        // Create the circle at the beginning of the cross
        const circle = new Ellipse(crossX, crossY, circleRadius, circleRadius, Art.dist/4);
        circle.setStyle({
            fillStyle: this.roof_color,
            strokeStyle: "#000000",
            lineWidth: 2*Art.grid_dist*0.02
        });
        circle.paint();

        // Get the current context
        const ctx = Art.ctx;

        // Set cross style
        this.setStyle({
            "strokeStyle":"#000000",
            "lineWidth":2*Art.grid_dist*0.02
        });

        // Draw the vertical line of the cross
        ctx.beginPath();
        Art.moveTo(crossX, crossY);
        Art.lineTo(crossX, crossY - crossHeight);

        // Draw the horizontal line of the cross
        Art.moveTo(crossX - crossWidth/2, crossY - crossHeight/2);
        Art.lineTo(crossX + crossWidth/2, crossY - crossHeight/2);

        // Render the cross
        ctx.stroke();
    }

}

export class SwedishDom{
    constructor(point) {
        let roof_angle = House.roof_angle;
        House.roof_angle = 2.5;

        // Store the current grid distance for scaling
        this.currentGridDist = Art.grid_dist;

        // Create the left church
        this.dom1 = new SwedishChurch(
            new Point(point.x, point.y),
            4,
            "dom"
        );

        // Create the right church
        this.dom2 = new SwedishChurch(
            new Point(point.x + 4 * Art.grid_dist, point.y),
            4,
            "dom"
        );

        // Call flipHorizontal to actually perform the flipping
        this.dom1.flipHorizontal();

        // Store the base point for the tower
        this.point = point;

        // Restore the original roof angle
        House.roof_angle = roof_angle;
    }

    paint(){
        // Paint the central tower first so it appears behind the churches
        this.paintCentralTower();

        // Then paint the two churches on top
        this.dom1.paint();
        this.dom2.paint();
    }

    paintCentralTower() {
        // Get the current context
        const ctx = Art.ctx;

        // Save current context state
        ctx.save();

        // Calculate tower dimensions and position
        const towerBaseWidth = 3.5 * this.currentGridDist; // Slightly wider base
        const towerHeight = 14 * this.currentGridDist; // Taller tower
        const towerRectHeight = 5 * this.currentGridDist; // Height of the rectangular part

        // Position the tower in the middle of the two churches horizontally
        const towerBaseX = this.point.x + 4 * this.currentGridDist - towerBaseWidth / 2;

        // Position the tower at the same level as the churches (the floor of the church)
        const towerBaseY = this.point.y;

        // Create a shadow for depth
        ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 5;
        ctx.shadowOffsetY = 5;

        // Draw the rectangular base of the tower
        const baseColor = SwedishChurch.TOWER_COLOR || "#333333"; // Dark gray
        ctx.fillStyle = baseColor;
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = this.currentGridDist * 0.02;

        // Rectangular base
        ctx.beginPath();
        ctx.rect(
            towerBaseX * Art.ratio,
            (towerBaseY - towerRectHeight) * Art.ratio,
            towerBaseWidth * Art.ratio,
            towerRectHeight * Art.ratio
        );
        ctx.fill();
        ctx.stroke();

        // Remove shadow for details
        ctx.shadowColor = 'transparent';

        // Add windows to the tower base
        this.drawTowerWindows(towerBaseX, towerBaseY, towerBaseWidth, towerRectHeight);

        // Restore shadow for the spire
        ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 5;
        ctx.shadowOffsetY = 5;

        // Draw the tower spire (tall triangle)
        // Use a darker color for the spire than the base
        const spireColor = Art.palette.getColorDarkerEx(baseColor, 0.2);
        ctx.fillStyle = spireColor;

        // Calculate spire dimensions
        const spireBaseWidth = towerBaseWidth * 0.9; // Slightly narrower than the base
        const spireBaseX = towerBaseX + (towerBaseWidth - spireBaseWidth) / 2;
        const spireBaseY = towerBaseY - towerRectHeight;
        const spireHeight = towerHeight - towerRectHeight;

        // Draw the spire
        ctx.beginPath();
        // Bottom left
        ctx.moveTo(spireBaseX * Art.ratio, spireBaseY * Art.ratio);
        // Top point
        ctx.lineTo((spireBaseX + spireBaseWidth / 2) * Art.ratio, (spireBaseY - spireHeight) * Art.ratio);
        // Bottom right
        ctx.lineTo((spireBaseX + spireBaseWidth) * Art.ratio, spireBaseY * Art.ratio);
        // Close the path
        ctx.closePath();
        ctx.fill();
        ctx.stroke();

        // Draw a gold cross at the very top
        this.drawTowerCross(spireBaseX + spireBaseWidth / 2, spireBaseY - spireHeight);

        // Restore context state
        ctx.restore();
    }

    // Draw arched windows on the tower
    drawTowerWindows(towerBaseX, towerBaseY, towerWidth, towerHeight) {
        const ctx = Art.ctx;

        // Window parameters
        const windowWidth = towerWidth * 0.2;
        const windowHeight = towerHeight * 0.4;
        const archHeight = windowHeight * 0.3;
        const windowSpacing = towerWidth * 0.1;

        // Calculate positions for three windows
        const startX = towerBaseX + (towerWidth - (3 * windowWidth + 2 * windowSpacing)) / 2;
        const windowY = towerBaseY - towerHeight + towerHeight * 0.3;

        // Draw three arched windows
        for (let i = 0; i < 3; i++) {
            const windowX = startX + i * (windowWidth + windowSpacing);

            // Draw the window with a dark blue color for stained glass effect
            ctx.fillStyle = "#000033";

            // Draw the rectangular part
            ctx.beginPath();
            ctx.rect(
                windowX * Art.ratio,
                windowY * Art.ratio,
                windowWidth * Art.ratio,
                (windowHeight - archHeight) * Art.ratio
            );
            ctx.fill();
            ctx.stroke();

            // Draw the arch
            ctx.beginPath();
            ctx.arc(
                (windowX + windowWidth / 2) * Art.ratio,
                (windowY + windowHeight - archHeight) * Art.ratio,
                (windowWidth / 2) * Art.ratio,
                Math.PI, 0, false
            );
            ctx.fill();
            ctx.stroke();
        }
    }

    drawTowerCross(x, y) {
        // Get the current context
        const ctx = Art.ctx;

        // Cross parameters - match the church cross dimensions
        const crossWidth = this.currentGridDist * 0.4;
        const crossHeight = this.currentGridDist * 0.8;
        const circleRadius = crossWidth * 0.3;

        // Use the same roof color for consistency
        const crossColor = window.roof_color || "#8B4513";

        // Create the circle at the beginning of the cross (like the church cross)
        ctx.beginPath();
        ctx.arc(
            x * Art.ratio,
            y * Art.ratio,
            circleRadius * Art.ratio,
            0,
            Math.PI * 2
        );
        ctx.fillStyle = crossColor;
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 2 * this.currentGridDist * 0.02;
        ctx.fill();
        ctx.stroke();

        // Set cross style
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 2 * this.currentGridDist * 0.02;

        // Draw the vertical line of the cross
        ctx.beginPath();
        ctx.moveTo(x * Art.ratio, y * Art.ratio);
        ctx.lineTo(x * Art.ratio, (y - crossHeight) * Art.ratio);

        // Draw the horizontal line of the cross
        ctx.moveTo((x - crossWidth/2) * Art.ratio, (y - crossHeight/2) * Art.ratio);
        ctx.lineTo((x + crossWidth/2) * Art.ratio, (y - crossHeight/2) * Art.ratio);

        // Render the cross
        ctx.stroke();
    }
}