import { Noise } from './Noise.js';
import { Art } from './Art.js';


export class Graphics {
    constructor() {
        this.noise = new Noise();
    }

    /**
     * Adds noise to an entire canvas
     * @param {CanvasRenderingContext2D} ctx - Canvas context to apply noise to
     * @param {number} intensity - Noise intensity (0-1)
     */
    addNoise(ctx, intensity=0.1) {
        const imageData = ctx.getImageData(0, 0, Art.width * Art.ratio, Art.height * Art.ratio);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
            // Use a smaller resolution value (0.005) to get proper variation between -1 and 1
            const value = this.noise.noise2D(i / 100, i / 100, 0.005) * 255 * intensity;
            data[i] += value;
            data[i + 1] += value;
            data[i + 2] += value;
        }
        ctx.putImageData(imageData, 0, 0);
    }

    /**
     * Creates a new pattern function with noise added to it
     * @param {Function} patternFn - Original pattern function that takes (x, y) and returns a color
     * @param {Object} options - Configuration options
     * @param {number} options.amount - Noise amount (0-1, default: 0.2)
     * @param {number} options.resolution - Noise resolution (default: 0.01)
     * @param {number} options.octaves - Number of fractal noise layers (default: 3)
     * @param {boolean} options.colorNoise - Whether to add different noise to R,G,B channels (default: false)
     * @returns {Function} - New pattern function with noise
     */
    addNoiseToPattern(patternFn, options = {}) {
        // Default options
        const {
            amount = 0.2,
            resolution = 0.01,
            octaves = 3,
            colorNoise = false
        } = options;

        // Create or use existing noise instance
        const noise = this.noise || new Noise();

        return (x, y) => {
            // Get the original color from the pattern
            const originalColor = patternFn(x, y);

            // If it's not a color string, return as is
            if (typeof originalColor !== 'string' || !originalColor.startsWith('#')) {
                return originalColor;
            }

            try {
                // Parse the hex color
                const r = parseInt(originalColor.slice(1, 3), 16);
                const g = parseInt(originalColor.slice(3, 5), 16);
                const b = parseInt(originalColor.slice(5, 7), 16);

                // Get noise values
                let noiseValue;
                let rNoise, gNoise, bNoise;

                if (colorNoise) {
                    // Generate different noise for each channel
                    rNoise = noise.fractal2DNormalized(x, y, resolution, octaves);
                    gNoise = noise.fractal2DNormalized(x + 100, y + 100, resolution, octaves);
                    bNoise = noise.fractal2DNormalized(x + 200, y + 200, resolution, octaves);
                } else {
                    // Use same noise for all channels (brightness variation)
                    noiseValue = noise.fractal2DNormalized(x, y, resolution, octaves);
                    // Convert from [0,1] to [-amount,amount]
                    noiseValue = (noiseValue - 0.5) * 2 * amount;
                }

                // Apply noise to channels
                let newR, newG, newB;

                if (colorNoise) {
                    // Each channel gets its own noise
                    newR = Math.max(0, Math.min(255, Math.round(r + (rNoise - 0.5) * 2 * amount * 255)));
                    newG = Math.max(0, Math.min(255, Math.round(g + (gNoise - 0.5) * 2 * amount * 255)));
                    newB = Math.max(0, Math.min(255, Math.round(b + (bNoise - 0.5) * 2 * amount * 255)));
                } else {
                    // All channels adjusted by same amount (preserves hue)
                    newR = Math.max(0, Math.min(255, Math.round(r + noiseValue * 255)));
                    newG = Math.max(0, Math.min(255, Math.round(g + noiseValue * 255)));
                    newB = Math.max(0, Math.min(255, Math.round(b + noiseValue * 255)));
                }

                // Convert back to hex
                return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
            } catch (e) {
                // If there's any error parsing the color, return the original
                return originalColor;
            }
        };
    }

    /**
     * Creates a noisy pattern directly from a base color
     * @param {string} baseColor - Base color in hex format
     * @param {Object} options - Options for noise (see addNoiseToPattern)
     * @returns {Function} - Pattern function that generates noisy colors
     */
    createNoisyPattern(baseColor, options = {}) {
        // Create a simple pattern function that returns the base color
        const patternFn = () => baseColor;

        // Apply noise to it
        return this.addNoiseToPattern(patternFn, options);
    }

    /**
     * Applies a noisy pattern to a shape
     * @param {Object} shape - Shape object with a paint method
     * @param {Function} patternFn - Pattern function (x,y) => color
     * @param {number} resolution - Resolution of the pattern in pixels
     * @returns {Object} - The shape with modified paint method
     */
    applyPatternToShape(shape, patternFn, resolution = 4) {
        // Store the original paint method
        const originalPaint = shape.paint;

        // Replace with a new paint method
        shape.paint = function(ctx) {
            // If it's a circular shape like Ellipse
            if (this.width === this.height && typeof this.x === 'number' && typeof this.y === 'number') {
                ctx.beginPath();
                ctx.arc(this.x * Art.ratio, this.y * Art.ratio, this.width * Art.ratio / 2, 0, Math.PI * 2);
                ctx.closePath();

                // Save original styles
                const origFillStyle = ctx.fillStyle;
                const origStrokeStyle = ctx.strokeStyle;

                // Fill with the pattern
                for (let px = this.x - this.width/2; px < this.x + this.width/2; px += resolution) {
                    for (let py = this.y - this.height/2; py < this.y + this.height/2; py += resolution) {
                        // Check if point is within the circle
                        const dx = px - this.x;
                        const dy = py - this.y;
                        const dist = Math.sqrt(dx*dx + dy*dy);

                        if (dist <= this.width/2) {
                            // Get color from pattern
                            const color = patternFn(px, py);

                            // Draw a small square with this color
                            ctx.fillStyle = color;
                            ctx.fillRect(
                                px * Art.ratio,
                                py * Art.ratio,
                                resolution * Art.ratio,
                                resolution * Art.ratio
                            );
                        }
                    }
                }

                // Restore styles and draw outline
                ctx.fillStyle = origFillStyle;
                ctx.strokeStyle = origStrokeStyle;

                if (this.style.strokeStyle) {
                    ctx.stroke();
                }
            } else {
                // For other shapes, just call the original paint method
                // You could extend this for rectangles, polygons, etc.
                originalPaint.call(this, ctx);
            }
        };

        return shape;
    }
}

