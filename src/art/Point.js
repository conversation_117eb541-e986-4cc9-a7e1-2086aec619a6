import {Art} from './Art.js'

// Base class for 2D points
export  class Point {
    constructor(x = 0, y = 0) {
      this.x = x;
      this.y = y;
    }
  
    add(point) {
      return new Point(this.x + point.x, this.y + point.y);
    }
    
    //d between 0 and 1
    getMiddle(point,d){
      return new Point(
        this.x+(point.x-this.x)*d,
        this.y+(point.y-this.y)*d
      )
    }

    move(x,y){
      return new Point(this.x+x,this.y+y)
    }
    
    movePolar(angle,dist){
      return new Point(
        this.x+dist*Math.cos(angle),
        this.y+dist*Math.sin(angle)
      )
    }

    distanceTo(point) {
      const dx = this.x - point.x;
      const dy = this.y - point.y;
      return Math.sqrt(dx * dx + dy * dy);
    }
  
    scale(factor) {
      return new Point(this.x * factor, this.y * factor);
    }
  
    clone() {
      return new Point(this.x, this.y);
    }

    debug(){
      Art.ctx.fillStyle = 'green';
      Art.ctx.fillRect(this.x*Art.ratio, this.y*Art.ratio, 5*Art.ratio, 5*Art.ratio);
    }
  }

