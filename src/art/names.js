import { Art } from './Art.js';

// Original Greenlandic place names for reference and random inclusion
const realNames = [
    "Nuuk", "Ilulissat", "Sisimiut", "Qaqortoq", "Aasiaat",
    "Maniitsoq", "Tasiilaq", "Paamiut", "Uummannaq", "Narsaq",
    "Kulusuk", "Ittoqqortoormiit", "Qeqertarsuaq", "Kangerlussuaq", "Nanortalik",
    "Ikerasak", "Qaarsut", "Niaqornat", "Saqqaq", "Oqaatsut"
  ];
  
  // Common patterns and elements in Greenlandic names
  const prefixes = ["Qa", "Nu", "Ilu", "Si", "Ma", "Ta", "Pa", "Uu", "Na", "Ku", "Itte", "Qe", "Ka", "Ni", "Ike", "O", "Aa"];
  const middleParts = ["qo", "lu", "si", "ni", "ma", "taa", "paa", "uma", "noo", "kaa", "qeq", "nga"];
  const suffixes = ["q", "k", "t", "miut", "ssuaq", "rtoq", "liaq", "siaat", "rnat", "rsut", "aaq", "nnaq"];
  
  export function generateGreenlandicName() {
    // 25% chance to return a real name from the list
    if (Art.rand() < 0.25) {
      const randomIndex = Math.floor(Art.rand() * realNames.length);
      return realNames[randomIndex];
    }
    
    // Otherwise generate a new name with similar patterns
    const prefix = prefixes[Math.floor(Art.rand() * prefixes.length)];
    
    // 70% chance to include a middle part
    const includeMiddle = Art.rand() < 0.7;
    const middle = includeMiddle ? middleParts[Math.floor(Art.rand() * middleParts.length)] : "";
    
    // 85% chance to include a suffix
    const includeSuffix = Art.rand() < 0.85;
    const suffix = includeSuffix ? suffixes[Math.floor(Art.rand() * suffixes.length)] : "";
    
    // Ensure double letters don't appear too frequently
    let name = prefix + middle + suffix;
    
    // Capitalize only the first letter
    return name.charAt(0).toUpperCase() + name.slice(1);
  }