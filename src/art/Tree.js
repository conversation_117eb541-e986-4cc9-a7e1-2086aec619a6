import { Drawable } from "./Drawable.js";
import { Art } from "./Art.js";
import { Tools } from "./Tools.js";
import { Point } from "./Point.js";
import { Path } from "./Path.js";
import { Style } from "./Style.js";

export class Tree extends Drawable {
    constructor(point, grid_w, maxLevels = 4) {
        super();
        this.point = point;
        this.grid_w = grid_w;
        this.maxLevels =2// Math.min(maxLevels, 5); // Limit max levels 
        this.branches = [];
        this.trunkLength = grid_w * Art.grid_dist * 1.5;
        
        this.grid_dist=Art.grid_dist
        
        
        // Generate the tree structure
        this.generateTree();
    }
    
    generateTree() {
        // Create a perfectly straight trunk
        const trunk = new Path();
        
        // Trunk points
        trunk.addPoint(this.point);
        trunk.addPoint(new Point(this.point.x, this.point.y - this.trunkLength));
        
        // Store level and set style
        trunk.level = 0;
    
        // Add trunk to branches list
        this.branches.push(trunk);
        
        // Recursively generate branches
        this.generateBranchesForPath(trunk, 1);

        this.generatePolygons()
    }
    
    // Function to get a point along a path at a given fraction (0-1)
    getPointAlongPath(path, fraction) {
        // Ensure fraction is between 0 and 1
        fraction = Math.max(0, Math.min(1, fraction));
        
        // For two-point paths
        if (path.points.length === 2) {
            const startPoint = path.points[0];
            const endPoint = path.points[1];
            
            return new Point(
                startPoint.x + (endPoint.x - startPoint.x) * fraction,
                startPoint.y + (endPoint.y - startPoint.y) * fraction
            );
        }
        
        // For multi-point paths
        const numSegments = path.points.length - 1;
        const totalDistance = this._getPathTotalLength(path);
        const targetDistance = totalDistance * fraction;
        
        let currentDistance = 0;
        
        for (let i = 0; i < numSegments; i++) {
            const start = path.points[i];
            const end = path.points[i + 1];
            
            const segmentLength = this._getDistanceBetweenPoints(start, end);
            
            if (currentDistance + segmentLength >= targetDistance) {
                // Target point is on this segment
                const segmentFraction = (targetDistance - currentDistance) / segmentLength;
                return new Point(
                    start.x + (end.x - start.x) * segmentFraction,
                    start.y + (end.y - start.y) * segmentFraction
                );
            }
            
            currentDistance += segmentLength;
        }
        
        // Fallback to last point
        return path.points[path.points.length - 1];
    }
    
    // Helper to get total path length
    _getPathTotalLength(path) {
        let length = 0;
        for (let i = 0; i < path.points.length - 1; i++) {
            length += this._getDistanceBetweenPoints(path.points[i], path.points[i + 1]);
        }
        return length;
    }
    
    // Helper to get distance between two points
    _getDistanceBetweenPoints(p1, p2) {
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    generateBranchesForPath(parentPath, level) {
        // Stop recursion if maxLevels reached
        if (level > this.maxLevels) return;
        
        // Get parent endpoints to calculate direction
        const parentStart = parentPath.points[0];
        const parentEnd = parentPath.points[parentPath.points.length - 1];
        const parentDir = Math.atan2(parentEnd.y - parentStart.y, parentEnd.x - parentStart.x);
        
        // Branch length decreases with level
        //param?
        const branchLength = this.trunkLength * (0.5 / Math.sqrt(level));
        
        // Branch thickness decreases with level (more aggressively)
        const thickness = parentPath.style.lineWidth * 0.75;
        
        // Determine symmetric branch positions based on level
        let branchPositions = [];
        
        if (level === 1) {
            // First level branches at specific positions
          //  branchPositions = [0.5, 0.7, 0.9];
            //params
            branchPositions = [0.5];
        } else if (level === 2) {
            // Second level branches at regular intervals
            branchPositions = [0.3, 0.5,0.7];
            //branchPositions = [0.5];
        } else {
            // Higher levels just branch at the ends
            branchPositions = [0.2,0.9];
        }
        
        // For each branch position
        for (const position of branchPositions) {
            // Get the point along the parent path
            const branchStartPoint = this.getPointAlongPath(parentPath, position);
            
            // Calculate symmetric angles for left and right branches
            const baseAngle = parentDir; // Default to parent direction
            const angleOffset = Math.PI * 0.25; // 45 degrees
            
            // Create symmetric branches
            for (let side = -1; side <= 1; side += 2) { // -1 for left, +1 for right
                // Skip center for a cleaner look
                if (side === 0) continue;
                
                const angle = baseAngle + (angleOffset * side);
                const branchEnd = new Point(
                    branchStartPoint.x + Math.cos(angle) * branchLength,
                    branchStartPoint.y + Math.sin(angle) * branchLength
                );
                
                // Create branch
                const branch = new Path();
                branch.addPoint(branchStartPoint);
                branch.addPoint(branchEnd);
                
                // Set branch properties
                branch.level = level;
         
                
                // Add to branches collection
                this.branches.push(branch);
                
                // Recursively generate branches
                this.generateBranchesForPath(branch, level + 1);
            }
            
            // For trunk only, add a center branch continuing upward
            if (level === 1 && position === 0.9) {
                const upAngle = -Math.PI/2; // Straight up
                const upEnd = new Point(
                    branchStartPoint.x + Math.cos(upAngle) * (branchLength * 0.7),
                    branchStartPoint.y + Math.sin(upAngle) * (branchLength * 0.7)
                );
                
                // Create upward branch
                const upBranch = new Path();
                upBranch.addPoint(branchStartPoint);
                upBranch.addPoint(upEnd);
                
                // Set branch properties
                upBranch.level = level;
                upBranch.style = this.trunkStyle.clone();
                upBranch.style.lineWidth = thickness;
                
                // Add to branches collection
                this.branches.push(upBranch);
                
                // Recursively generate branches
                this.generateBranchesForPath(upBranch, level + 1);
            }
        }
    }
    
    generatePolygons(){
            // Performance: Pre-sort branches by level once
            if (!this.branchesSorted) {
                this.branches.sort((a, b) => a.level - b.level);
                this.branchesSorted = true;
            }

            this.polygons=[]

            let w=this.grid_dist*4*0.01
            // Draw branches
            for (const branch of this.branches) {
                let poly=Path.extrude(branch,w,[1,0.5])
                poly.setStyle(
                    {
                        fillStyle: "#000000",
                        strokeStyle: "#ffffff",
                        lineWidth: 4*this.grid_dist*0.01
                    }
                )

                
             
                this.polygons.push(poly)
              
            }
        
            
    }


    paint() {
        this.polygons.forEach(p=>{
            p.stroke()
        })
        this.polygons.forEach(p=>{
            p.fill()
        })
    }
}