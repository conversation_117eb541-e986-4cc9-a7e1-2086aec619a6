import { Tools } from './Tools.js';
import { Art } from './Art.js';
import { Noise } from './Noise.js';

export class Palette {
    constructor(colors = {}) {
        this.colors = colors;
        this.noise= new Noise();
    }

    getColor(index,type=null) {
        if(type==null) type="main"
        let colors=this.colors[type]

         if(colors==null) return "#000000"
        if (index < 0 || index >= colors.length) {
            throw new Error('Index out of bounds');
        }
        return colors[index];
    }
      getRandColor(type=null, x=0, y=0,dist=50) {
        if(type==null) type="main";

        let colors=this.colors[type];
        if(colors==null) return "#000000";

        let selectedColor;
        if(x==0 && y==0){
            selectedColor = Tools.getRandFromArray(colors);
        } else {

            // Get noise value in range [-1, 1]

            dist=dist
           // const p=0.0001
            const p=1
            let noiseValue = this.noise.noise2D(x-Art.width/2, y-Art.height/2, p*dist*0.5);

            if(Art.rand()<0.01)console.log("noiseValue",noiseValue)
            // Map from [-1, 1] to [0, colors.length-1]
            // First convert from [-1, 1] to [0, 1]
            let normalizedValue = (noiseValue + 1) / 2;

            // Then scale to [0, colors.length-1]
            let index = Math.floor(normalizedValue * colors.length);

            // Ensure index is within bounds
            index = Math.max(0, Math.min(colors.length - 1, index));

            selectedColor = colors[index];
        }

        // Apply day_time modifications to the selected color
        return this.applyDayTimeModification(selectedColor, type);
    }

    /**
     * Apply day_time modifications to colors based on the $fx.getParam("day_time") value
     * @param {string} color - The original color in hex format
     * @param {string} type - The color type (sky, house, landscape, roof, etc.)
     * @returns {string} - The modified color based on day_time
     */
    applyDayTimeModification(color, type) {
        // Validate input color
        if (!color || typeof color !== 'string') {
            console.warn('Invalid color input to applyDayTimeModification:', color);
            return color || "#000000"; // Return original or fallback
        }

        // Get the day_time parameter from $fx
        const dayTime = typeof $fx !== 'undefined' ? $fx.getParam("day_time") : "day";

        // If it's day time, return the original color unchanged
        if (dayTime === "day") {
            return color;
        }

        // Define modification factors for different times and color types
        let darkeningFactor = 0;
        let colorShift = null;

        switch (dayTime) {
            case "morning":
                // Morning: slight warm tint and very slight darkening
                switch (type) {
                    case "sky":
                        // Morning sky gets a warm, golden tint
                        colorShift = { r: 1.1, g: 1.05, b: 0.9 }; // Warm golden tint
                        darkeningFactor = 0.1;
                        break;
                    case "house":
                    case "roof":
                    case "landscape":
                        // Other elements get slightly warmer and slightly darker
                        colorShift = { r: 1.05, g: 1.02, b: 0.95 };
                        darkeningFactor = 0.05;
                        break;
                    default:
                        darkeningFactor = 0.05;
                        break;
                }
                break;

            case "evening":
                // Evening: warm orange/red tint and moderate darkening
                switch (type) {
                    case "sky":
                        // Evening sky gets warm orange/red tint
                        colorShift = { r: 1.2, g: 0.9, b: 0.7 }; // Orange/red tint
                        darkeningFactor = 0.2;
                        break;
                    case "house":
                    case "roof":
                    case "landscape":
                        // Other elements get warmer and moderately darker
                        colorShift = { r: 1.1, g: 0.95, b: 0.8 };
                        darkeningFactor = 0.15;
                        break;
                    default:
                        darkeningFactor = 0.15;
                        break;
                }
                break;

            case "night":
                // Night: much darker, blue tint
                switch (type) {
                    case "sky":
                        // Night sky becomes much darker with blue tint
                        colorShift = { r: 0.3, g: 0.4, b: 0.8 }; // Blue tint
                        darkeningFactor = 0.8; // Much darker
                        break;
                    case "house":
                    case "roof":
                    case "landscape":
                        // Other elements become much darker with slight blue tint
                        colorShift = { r: 0.7, g: 0.75, b: 0.9 };
                        darkeningFactor = 0.6; // Pretty darker
                        break;
                    default:
                        darkeningFactor = 0.6;
                        break;
                }
                break;

            default:
                return color; // Unknown day_time, return original
        }

        // Apply the modifications
        let modifiedColor = color;

        // Apply color shift if specified
        if (colorShift) {
            modifiedColor = this.applyColorShift(modifiedColor, colorShift);
            // Validate the result
            if (!modifiedColor || modifiedColor.includes('NaN')) {
                console.warn('Color shift produced invalid result, using original:', color);
                modifiedColor = color;
            }
        }

        // Apply darkening
        if (darkeningFactor > 0) {
            modifiedColor = this.getColorDarkerEx(modifiedColor, darkeningFactor);
            // Validate the result
            if (!modifiedColor || modifiedColor.includes('NaN')) {
                console.warn('Color darkening produced invalid result, using original:', color);
                modifiedColor = color;
            }
        }

        return modifiedColor;
    }

    /**
     * Apply a color shift (tint) to a color
     * @param {string} color - The original color in hex format
     * @param {Object} shift - Object with r, g, b multipliers
     * @returns {string} - The shifted color
     */
    applyColorShift(color, shift) {
        // Validate input color
        if (!color || typeof color !== 'string' || !color.startsWith('#') || color.length !== 7) {
            console.warn('Invalid color format:', color);
            return color; // Return original if invalid
        }

        // Convert hex to RGB with validation
        let r = parseInt(color.slice(1, 3), 16);
        let g = parseInt(color.slice(3, 5), 16);
        let b = parseInt(color.slice(5, 7), 16);

        // Check for NaN values
        if (isNaN(r) || isNaN(g) || isNaN(b)) {
            console.warn('Failed to parse color:', color);
            return color; // Return original if parsing failed
        }

        // Validate shift object
        if (!shift || typeof shift !== 'object' ||
            typeof shift.r !== 'number' || typeof shift.g !== 'number' || typeof shift.b !== 'number' ||
            isNaN(shift.r) || isNaN(shift.g) || isNaN(shift.b)) {
            console.warn('Invalid shift object:', shift);
            return color; // Return original if shift is invalid
        }

        // Apply the shift
        r = Math.max(0, Math.min(255, Math.round(r * shift.r)));
        g = Math.max(0, Math.min(255, Math.round(g * shift.g)));
        b = Math.max(0, Math.min(255, Math.round(b * shift.b)));

        // Final validation check
        if (isNaN(r) || isNaN(g) || isNaN(b)) {
            console.warn('Color calculation resulted in NaN:', { original: color, shift, result: { r, g, b } });
            return color; // Return original if calculation failed
        }

        // Convert back to hex
        return '#' +
            r.toString(16).padStart(2, '0') +
            g.toString(16).padStart(2, '0') +
            b.toString(16).padStart(2, '0');
    }

    /**
 * Generates a random color with fixed brightness in hexadecimal format
 * @param {number} brightness - Value between 0 and 1 (0 = dark, 1 = bright)
 * @returns {string} Hexadecimal color string (e.g. "#FF5A32")
 */
     getRealRandomColor(brightness = 0.7) {
        // Clamp brightness between 0 and 1
        brightness = Math.max(0, Math.min(1, brightness));

        // Convert brightness to HSL lightness value (0-100)
        const lightness = brightness * 100;

        // Generate random hue (0-360)
        const hue = Math.floor(Art.rand() * 360);

        // Generate random saturation (50-100 for more vibrant colors)
        const saturation = 50 + Math.floor(Art.rand() * 50);

        // Convert HSL to RGB
        const h = hue / 360;
        const s = saturation / 100;
        const l = lightness / 100;

        let r, g, b;

        if (s === 0) {
        r = g = b = l; // achromatic
        } else {
        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };

        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;

        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
        }

        // Convert RGB to hex
        const toHex = (x) => {
        const hex = Math.round(x * 255).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
        };

        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    }
    addNamedColor(name, color) {
        this.colors[name] = color;
    }

    /*
    addColor(color) {
        this.colors.push(color);
    }

    removeColor(index) {
        if (index < 0 || index >= this.colors.length) {
            throw new Error('Index out of bounds');
        }
        this.colors.splice(index, 1);
    }*/



    getColorDarker(index,type=null, amount) {
        console.log("getColorDarker",index,type,amount)
        if(type==null) type="main"
        let colors=this.colors[type]
        // Validate inputs
        if (index < 0 || index >= colors.length) {
            throw new Error('Index out of bounds');
        }
        if (amount < 0 || amount > 1) {
            throw new Error('Amount must be between 0 and 1');
        }

        let color = colors[index];
        return this.getColorDarkerEx(color, amount);

    }


    getColorDarkerEx(color,amount) {
        // Convert hex to RGB
        let r = parseInt(color.slice(1, 3), 16);
        let g = parseInt(color.slice(3, 5), 16);
        let b = parseInt(color.slice(5, 7), 16);

        // Perceptive luminance weights
        // These weights reflect how human eyes perceive color brightness
        const luminanceWeights = [0.299, 0.587, 0.114];

        // Calculate original luminance
        const originalLuminance =
            r * luminanceWeights[0] +
            g * luminanceWeights[1] +
            b * luminanceWeights[2];

        // Calculate new luminance
        const newLuminance = originalLuminance * (1 - amount);

        // Calculate scaling factor
        const luminanceRatio = newLuminance / originalLuminance;

        // Scale RGB components proportionally
        r = Math.max(0, Math.min(255, Math.round(r * luminanceRatio)));
        g = Math.max(0, Math.min(255, Math.round(g * luminanceRatio)));
        b = Math.max(0, Math.min(255, Math.round(b * luminanceRatio)));

        // Convert back to hex
        return '#' +
            r.toString(16).padStart(2, '0') +
            g.toString(16).padStart(2, '0') +
            b.toString(16).padStart(2, '0');
    }

    getColorLighterEx(color, amount) {
        // Convert hex to RGB
        let r = parseInt(color.slice(1, 3), 16);
        let g = parseInt(color.slice(3, 5), 16);
        let b = parseInt(color.slice(5, 7), 16);

        // Calculate how much room we have to lighten each component
        const headroom = [
            255 - r,
            255 - g,
            255 - b
        ];

        // Apply the lightening amount proportionally to each component
        r = Math.min(255, Math.round(r + headroom[0] * amount));
        g = Math.min(255, Math.round(g + headroom[1] * amount));
        b = Math.min(255, Math.round(b + headroom[2] * amount));

        // Convert back to hex
        return '#' +
            r.toString(16).padStart(2, '0') +
            g.toString(16).padStart(2, '0') +
            b.toString(16).padStart(2, '0');
    }

    //for a generic fillStyle
    static adjustBrightness(color, amount) {
        // Create a temporary canvas to parse the color
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        const ctx = canvas.getContext('2d');

        // Set the fillStyle and fill a pixel to get the computed color
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, 1, 1);

        // Get the pixel data (RGBA values)
        const pixelData = ctx.getImageData(0, 0, 1, 1).data;

        // Extract RGB values
        let r = pixelData[0];
        let g = pixelData[1];
        let b = pixelData[2];
        const a = pixelData[3] / 255; // Convert alpha to 0-1 range

        // Adjust brightness
        if (amount < 0) {
          // Darken
          const factor = 1 + amount;
          r = Math.max(0, Math.round(r * factor));
          g = Math.max(0, Math.round(g * factor));
          b = Math.max(0, Math.round(b * factor));
        } else {
          // Lighten
          const factor = amount;
          r = Math.min(255, Math.round(r + (255 - r) * factor));
          g = Math.min(255, Math.round(g + (255 - g) * factor));
          b = Math.min(255, Math.round(b + (255 - b) * factor));
        }

        // Return the new color as rgba
        return `rgba(${r}, ${g}, ${b}, ${a})`;
      }

      /*
    get length() {
        return this.colors.length;
    }*/
}


 let palette = new Palette({
            "main": [
                '#D4D3C9',
                '#A0B3B7',
                '#40629A',
                '#548093',
                '#FFFFFF' // Added white for snow
            ],
            "landscape2": [
                "#856A7E",
                "#766D75",
                "#A59B90"
            ],
            "landscape2": [
                "#B6A787",
                "#A59B90",

            ],
            "roof": [
               // '#282629',

                '#B5785D',
                '#B6A787',
                '#ffffff',

                '#92ACB4',
                '#C5D4DD',
                '#F2E2CE'
            ],
            "landscape": [
                //'#333333',
                "#FFFFFF", // Added white for snow
                "#F2F2F2", // Added light grey for snow variation
                "#E6E6E6", // Added another light grey for snow variation
                "#CCCCCC", // Added grey for snow variation
                "#E6E6E6", // Added darker grey for snow variation



            ],

            "sky": [
                '#ffffff',
                '#000000',
                '#92ACB4',
                '#C5D4DD',
                '#F2E2CE'
            ],
            "house": [ //param fusta o colors o les dos
            /*    '#8B4513',  // Saddle Brown
                '#CD853F',  // Peru
                '#D2691E',  // Chocolate
                '#A0522D',  // Sienna
                '#DEB887',  // Burlywood
                '#F4A460',  // Sandy Brown
                '#D2B48C',  // Tan
                '#FFDAB9', // Peach Puff
                */

                '#0A5C2A',  // More saturated dark green
                '#FFD700',  // More saturated gold
                '#FF4500',  // More saturated red-orange
                '#1E90FF',  // More saturated blue
                '#E0E0E0',   // Kept light grey as is for contrast
                '#4A6D7C',   // Nordic blue-grey
                '#8A9A5B'    // Nordic sage green
            ]
        });



       Art.palette=palette