import { Art } from './Art.js';
import { Point } from './Point.js';

export  class Canvas {
    constructor(canvas) {
        this.canvas = canvas;
        Art.ctx = canvas.getContext('2d');
        Art.ctx.lineCap = 'round';
        Art.ctx.lineJoin = 'round';

        this.width =Art.width;
        this.height = Art.height;
        this.canvas.width = Art.width*Art.ratio;
        this.canvas.height = Art.height*Art.ratio;

        this.lt=new Point(0,0)
        this.rt=new Point(Art.width,0)
        this.lb=new Point(0,Art.height)
        this.rb=new Point(Art.width,Art.height)

    }

    setRatio(ratio){
        Art.ratio = ratio;
        this.canvas.width = Art.width*Art.ratio;
        this.canvas.height = Art.height*Art.ratio;
      
    }

 }
 