import { Art, Canvas, Point, Scene, SwedishChurch } from './art/index.js';

// Initialize canvas
const canvas = document.getElementById('canvas');
Art.width = 1920;
Art.height = 1080;
const mycanvas = new Canvas(canvas);
mycanvas.setRatio(1);

// Set grid distance
Art.grid_dist = 50;

// Create a scene
const scene = new Scene(canvas);

// Create churches with different widths to showcase variety
const churches = [];

// Create a row of churches with different widths
for (let i = 0; i < 4; i++) {
    const width = i + 3; // Widths from 3 to 6
    const church = new SwedishChurch(
        new Point(200 + i * 400, 600),
        width
    );
    
    // Flip every other church for variety
    if (i % 2 === 1) {
        church.flipHorizontal();
    }
    
    churches.push(church);
    scene.add(church, 0);
}

// Create a second row of churches to show more variety
for (let i = 0; i < 4; i++) {
    const width = i + 3; // Widths from 3 to 6
    const church = new SwedishChurch(
        new Point(200 + i * 400, 300),
        width
    );
    
    // Flip every other church for variety
    if (i % 2 === 0) {
        church.flipHorizontal();
    }
    
    churches.push(church);
    scene.add(church, 0);
}

// Paint the scene
scene.paint();

// Add a title
Art.ctx.font = '24px Arial';
Art.ctx.fillStyle = '#000';
Art.ctx.fillText('Enhanced Swedish Churches with Various Styles', 20, 50);
Art.ctx.font = '18px Arial';
Art.ctx.fillText('Features: Steeples, Multiple Cross Styles, Decorative Elements, Church-style Windows', 20, 80);
