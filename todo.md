to-do juny 2025
--

- afegir modes de color, les portes no poden ser blaves, catedrals posar del seu color

- monticuls que fer segons estacio?



llista final
--

- mode nit 
- arreglar ratlles o treure
- poder activar amb tecla. comprar boto
- millorar presentacio dels noms 
- millorar varietat de colors
- mode nit
- mode amb mar en primer terme
- poder variar tot amb parametres
   - posicio i mida lluna
   - distribucio cases
   - inclinacio perfils muntanyes
- color esglesia no es sempre negre
- testejar tracking 
- provar reinici raspberry etc
- escriure un text descriptiu




s16 2025
--
- la distribucio de colors no es correcta


+ prova de dibuixar linies paraleles a alguns layers 
+ baixar saturacio a capes mes llunyanes 
+ millorar esglesia i domkyrka 
- millorar trackinh i integrar 
+ generacio de noms 
+ config refresca automaticament i tecla activa


- millorar el meu web 
- revisar convos 


bugs 
--

- no funciona be al canviar ratio, rellotgs i sol per exemple

s15 2025
--

- arreglar funcio de soroll o fer servir llibreria, ha de tornar -1 a 1
- fer servir per distribucio de cases i coses i pels colors
- fer extrude i millorar arbres

- fer captacio posicio camera
- millorar el parallax
- fer proves de moviment

s14 2025
--

- integracio raspberry pi 
- deteccio camara i provar parallax 
- tornar a posar esglesies 
- crear esglesia nova 
- finestres al frontwall
- prova de clusters, que funcioni, colors i cases
- estiu i hivern 

s13 2025
--

+ els punts no visibles estan malament, debugar i arreglar 
+ fer layers mes grans per poder moure
+ fer moviment amb ratoli (pensant que després serà camara)
+ solucionar el tema paralax, cada layer ha de ser més gran per poder-se moure, comprovar que converteix bé a bitmap i es pot moure 
+ millores gràfiques

s11 2025
---
1+ punts a les capes i detectar de veritat els que estan per sota
1- profunditat
2- colors sostres 
3- terra de neu

boring:
- refactor de codi, no poden haver coses repetides 



+ pensar bé com fer refactoring tema distribucio de cases 
+ fer variacions de les mides de les cases amb diferents colors 


+ fer layers de landscape amb angles de tendencia 

+ algorisme per crear portes i finestres 



- fer cases molt simples tipus cadaques, espaiar tots els elements
sempre igual perquè es pugui sobreposar, fer una prova amb tot plé

- fer finestres i totes les variacions possibles de les cases

- a partir d'esbossos crear cases i potser altres formes 
cases sueques i de cadaques

- fer paths de diferents maneres
   getPolarLine(p, angle, distance, turn)

- gestió de colors?
- funcio per convertir polygons sempre a pasos

per més endevant 
--

- fer marc xulo copiant algun exemple
- mostrar avisos també com en algun exemple


bugs 
--

- linies blanques rares quan ratio < 1
fet
--

s8
--
+ afegir a git 

+ tema escena, afegir les coses i que es pintin 
` provar el tema dels multipolygons 
+ implementar tema ratio i comprovar manté escala
+ implementar tema mida canvas 


+ path ha de tenir trace i stroke 
+ polygon fill (que és trace i fill)
+ multipolygon es drawable 

+ dibuixar una casa simple


dades 
--

canviat a fxhash 

npx fxhash dev



antic 
--
npm run start
npm run build

esborrar cache parcel 
rm -rf .parcel-cache

git 
--

esborrar els darrers canvis 


acronym
--

Major Towns & Settlements
Nuuk – The capital and largest city
Ilulissat – Famous for its icefjord (UNESCO site)
Sisimiut – The second-largest town, known for outdoor activities
Qaqortoq – A picturesque southern town
Aasiaat – A fishing town in western Greenland
Maniitsoq – Known as the "Venice of Greenland"
Tasiilaq – The largest town in East Greenland
Paamiut – A small port town
Uummannaq – An island town with a heart-shaped mountain
Narsaq – Known for its lush green landscapes
Smaller Villages & Settlements
Kulusuk – A gateway to East Greenland
Ittoqqortoormiit – One of the most isolated settlements in the world
Qeqertarsuaq – Located on Disko Island
Kangerlussuaq – Known for its international airport
Nanortalik – Greenland’s southernmost town
Ikerasak – A remote village near Uummannaq
Qaarsut – A small settlement with an airport
Niaqornat – A tiny village with less than 50 residents
Saqqaq – An ancient Inuit archaeological site
Oqaatsut – A small village north of Ilulissat

Nuuk – The capital and largest city
Ilulissat – Famous for its icefjord (UNESCO site)
Sisimiut – The second-largest town, known for outdoor activities
Qaqortoq – A picturesque southern town
Aasiaat – A fishing town in western Greenland
Maniitsoq – Known as the "Venice of Greenland"
Tasiilaq – The largest town in East Greenland
Paamiut – A small port town
Uummannaq – An island town with a heart-shaped mountain
Narsaq – Known for its lush green landscapes
Smaller Villages & Settlements
Kulusuk – A gateway to East Greenland
Ittoqqortoormiit – One of the most isolated settlements in the world
Qeqertarsuaq – Located on Disko Island
Kangerlussuaq – Known for its international airport
Nanortalik – Greenland’s southernmost town
Ikerasak – A remote village near Uummannaq
Qaarsut – A small settlement with an airport
Niaqornat – A tiny village with less than 50 residents
Saqqaq – An ancient Inuit archaeological site
Oqaatsut – A small village north of Ilulissat


Nuuk
Ilulissat
Sisimiut
Qaqortoq
Aasiaat
Maniitsoq
Tasiilaq
Paamiut
Uummannaq
Narsaq
Kulusuk
Ittoqqortoormiit
Qeqertarsuaq
Kangerlussuaq
Nanortalik
Ikerasak
Qaarsut
Niaqornat
Saqqaq
Oqaatsut
